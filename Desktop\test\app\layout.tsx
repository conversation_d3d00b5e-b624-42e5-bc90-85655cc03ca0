import type React from "react"
import type { Metada<PERSON> } from "next"
import "./globals.css"

export const metadata: Metadata = {
  title: "AfriCola Energy - Two Shores. One Energy.",
  description:
    "AfriCola Energy bridges Tunisian heritage with Italian design. Experience the Mediterranean energy revolution with three legendary flavors: Kola Nut, Classic, and Light.",
  keywords:
    "AfriCola, Energy Drink, Tunisia, Italia, Mediterranean, Kola Nut, Energy, Cultural Fusion, Premium Beverages",
  authors: [{ name: "AfriCola International" }],
  viewport: "width=device-width, initial-scale=1",
  openGraph: {
    title: "AfriCola Energy - Two Shores. One Energy.",
    description: "Mediterranean energy revolution bridging Tunisia and Italy",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "AfriCola Energy - Mediterranean Energy Revolution",
    description: "Three legendary flavors. One cultural bridge. 🇹🇳⚡🇮🇹",
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className="antialiased">{children}</body>
    </html>
  )
}
