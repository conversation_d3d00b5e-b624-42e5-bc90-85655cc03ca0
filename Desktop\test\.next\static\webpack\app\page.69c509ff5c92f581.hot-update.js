"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AfriColaWebsite; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction AfriColaWebsite() {\n    _s();\n    const heroRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX,\n                y: e.clientY\n            });\n        };\n        const handleScroll = ()=>{\n            setScrollY(window.scrollY);\n            // 3D Parallax for can\n            if (canRef.current) {\n                const scrolled = window.pageYOffset;\n                const rate = scrolled * -0.3;\n                const rotate = scrolled * 0.2;\n                canRef.current.style.transform = \"\\n          translateY(\".concat(rate, \"px) \\n          rotateX(\").concat(rotate, \"deg) \\n          rotateY(\").concat(rotate * 0.5, \"deg)\\n          scale(\").concat(1 + scrolled * 0.0005, \")\\n        \");\n            }\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>{\n            window.removeEventListener(\"mousemove\", handleMouseMove);\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, []);\n    const scrollToSection = (sectionId)=>{\n        var _document_getElementById;\n        (_document_getElementById = document.getElementById(sectionId)) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black text-white overflow-x-hidden relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 pointer-events-none z-0\",\n                children: [\n                    ...Array(50)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute w-1 h-1 bg-yellow-400 rounded-full animate-float\",\n                        style: {\n                            left: \"\".concat(Math.random() * 100, \"%\"),\n                            top: \"\".concat(Math.random() * 100, \"%\"),\n                            animationDelay: \"\".concat(Math.random() * 5, \"s\"),\n                            animationDuration: \"\".concat(3 + Math.random() * 4, \"s\")\n                        }\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed w-8 h-8 bg-yellow-400/30 rounded-full pointer-events-none z-50 mix-blend-difference transition-all duration-300 ease-out\",\n                style: {\n                    left: mousePosition.x - 16,\n                    top: mousePosition.y - 16,\n                    transform: \"scale(\".concat(1 + Math.sin(Date.now() * 0.005) * 0.3, \")\")\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 w-full z-50 bg-black/20 backdrop-blur-xl border-b border-yellow-400/30\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl font-black text-yellow-400 tracking-wider glitch-text\",\n                                children: \"ȺFⱤƗȻ\\xd8ŁȺ\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex space-x-8\",\n                                children: [\n                                    \"HOME\",\n                                    \"PRODOTTO\",\n                                    \"IDENTIT\\xc0\",\n                                    \"ACQUISTA\"\n                                ].map((item, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToSection(item.toLowerCase().replace(\"\\xe0\", \"a\")),\n                                        className: \"relative group px-4 py-2 text-sm font-bold tracking-wider hover:text-yellow-400 transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10\",\n                                                children: item\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-yellow-400/20 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"home\",\n                ref: heroRef,\n                className: \"relative min-h-screen flex items-center justify-center overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-background\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"blob blob-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"blob blob-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"blob blob-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lightning lightning-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lightning lightning-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lightning lightning-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 max-w-7xl mx-auto px-6 grid lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center lg:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-yellow-400 text-xl font-bold mb-4 tracking-[0.3em] animate-pulse\",\n                                                children: \"⚡ MEDITERRANEAN ENERGY ⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-6xl md:text-8xl font-black mb-6 leading-none\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block text-gradient-1 animate-text-glow\",\n                                                        children: \"L'ENERGIA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block text-gradient-2 animate-text-glow delay-300\",\n                                                        children: \"CHE NASCE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block text-yellow-400 animate-text-glow delay-600 text-shadow-glow\",\n                                                        children: \"DAL MEDITERRANEO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl md:text-2xl text-gray-300 leading-relaxed font-light\",\n                                                children: [\n                                                    \"AfriCola Energy non \\xe8 una semplice bevanda: \\xe8 un'\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold animate-pulse\",\n                                                        children: \"esperienza\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \".\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg text-gray-400 leading-relaxed\",\n                                                children: \"Creata in Tunisia e ispirata dal design italiano, questa lattina gialla sprigiona una carica intensa grazie all'estratto naturale di noce di cola.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"group relative overflow-hidden bg-gradient-to-r from-yellow-400 to-yellow-600 text-black px-12 py-6 text-xl font-black rounded-full hover:shadow-2xl hover:shadow-yellow-400/50 transition-all duration-500 transform hover:scale-110 hover:-rotate-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"relative z-10 flex items-center\",\n                                                        children: [\n                                                            \"PROVALA ORA\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-3 group-hover:translate-x-2 transition-transform text-2xl\",\n                                                                children: \"⚡\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-yellow-600 to-yellow-400 translate-x-full group-hover:translate-x-0 transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-0.5 bg-yellow-400 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-yellow-400 font-bold text-lg tracking-wider\",\n                                                        children: \"SENTILA TUA. VIVI AFRICOLA ENERGY.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-0.5 bg-yellow-400 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative perspective-1000\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: canRef,\n                                        className: \"relative transform-gpu\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-full bg-gradient-to-r from-yellow-400/30 to-transparent animate-spin-slow blur-xl scale-150\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-96 h-96 border-2 border-yellow-400/30 rounded-full animate-ping\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute w-80 h-80 border border-yellow-400/20 rounded-full animate-ping delay-1000\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute w-64 h-64 border border-yellow-400/10 rounded-full animate-ping delay-2000\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 can-3d\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"/africola-can.png\",\n                                                    alt: \"AfriCola Energy Can\",\n                                                    className: \"w-96 h-auto drop-shadow-2xl hover:scale-110 transition-all duration-700 transform-gpu\",\n                                                    style: {\n                                                        filter: \"drop-shadow(0 0 30px rgba(255, 204, 0, 0.5))\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-10 -left-10 w-4 h-4 bg-yellow-400 rounded-full animate-bounce delay-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-20 -right-8 w-3 h-3 bg-yellow-400 rounded-full animate-bounce delay-700\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-32 right-12 w-2 h-2 bg-yellow-400 rounded-full animate-bounce delay-1000\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"scroll-indicator\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"scroll-wheel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"prodotto\",\n                className: \"py-32 bg-gradient-to-b from-black via-gray-900 to-black relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hexagon-pattern opacity-10\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-6 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-400 text-sm font-bold tracking-[0.3em] uppercase border border-yellow-400/30 px-6 py-2 rounded-full\",\n                                            children: \"⚡ LA FORMULA PERFETTA ⚡\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-5xl md:text-7xl font-black mb-8 leading-tight\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-gradient-3\",\n                                                children: \"LA NOSTRA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-yellow-400 text-shadow-glow\",\n                                                children: \"FORMULA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-gradient-4\",\n                                                children: \"LA TUA FORZA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed\",\n                                        children: \"Ogni lattina di AfriCola Energy \\xe8 pensata per darti uno slancio naturale, duraturo e intenso.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\",\n                                children: [\n                                    {\n                                        title: \"ALTA DOSE DI CAFFEINA\",\n                                        icon: \"⚡\",\n                                        color: \"from-yellow-400 to-orange-500\"\n                                    },\n                                    {\n                                        title: \"ESTRATTO NATURALE DI NOCE DI COLA\",\n                                        icon: \"\\uD83C\\uDF30\",\n                                        color: \"from-amber-400 to-yellow-600\"\n                                    },\n                                    {\n                                        title: \"SENZA COMPROMESSI\",\n                                        icon: \"\\uD83D\\uDCAA\",\n                                        color: \"from-yellow-500 to-red-500\"\n                                    },\n                                    {\n                                        title: \"PRODOTTA IN TUNISIA\",\n                                        icon: \"\\uD83C\\uDDF9\\uD83C\\uDDF3\",\n                                        color: \"from-red-500 to-yellow-400\"\n                                    },\n                                    {\n                                        title: \"DESIGN ITALIANO\",\n                                        icon: \"\\uD83C\\uDFA8\",\n                                        color: \"from-green-500 to-yellow-400\"\n                                    }\n                                ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group relative feature-card transform-gpu\",\n                                        style: {\n                                            animationDelay: \"\".concat(index * 200, \"ms\")\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-3xl backdrop-blur-sm border border-yellow-400/20 group-hover:border-yellow-400/50 transition-all duration-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br \".concat(feature.color, \" opacity-0 group-hover:opacity-20 rounded-3xl transition-all duration-500\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 p-8 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-6xl mb-6 group-hover:scale-125 transition-transform duration-500 animate-bounce-slow\",\n                                                        children: feature.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-yellow-400 mb-4 leading-tight\",\n                                                        children: feature.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-1 bg-yellow-400 mx-auto rounded-full group-hover:w-20 transition-all duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center space-x-4 bg-gradient-to-r from-yellow-400/20 to-transparent p-8 rounded-full border border-yellow-400/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 bg-yellow-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-400 font-bold tracking-wider\",\n                                            children: \"ENERGIA PURA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 bg-yellow-400 rounded-full animate-pulse delay-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-400 font-bold tracking-wider\",\n                                            children: \"GUSTO AUTENTICO\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 bg-yellow-400 rounded-full animate-pulse delay-1000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"identita\",\n                className: \"py-32 bg-gradient-to-b from-black to-gray-900 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"wave-pattern\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-6 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-5xl md:text-7xl font-black mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-gradient-5\",\n                                            children: \"UN PONTE TRA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-yellow-400 text-shadow-glow\",\n                                            children: \"DUE CULTURE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-2 gap-16 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-300 leading-relaxed\",\n                                                children: \"AfriCola Energy nasce dall'incontro tra due mondi: la forza e la vitalit\\xe0 della Tunisia, e la raffinatezza creativa dell'Italia.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"group flex items-center space-x-6 p-6 bg-gradient-to-r from-red-600/20 to-transparent rounded-2xl border border-red-500/30 hover:border-red-500/60 transition-all duration-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-red-600 rounded-full flex items-center justify-center text-3xl group-hover:scale-110 transition-transform\",\n                                                                children: \"\\uD83C\\uDDF9\\uD83C\\uDDF3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-2xl font-bold text-red-400\",\n                                                                        children: \"TUNISIA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-300\",\n                                                                        children: \"Forza e Vitalit\\xe0 del Mediterraneo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"group flex items-center space-x-6 p-6 bg-gradient-to-r from-green-600/20 to-transparent rounded-2xl border border-green-500/30 hover:border-green-500/60 transition-all duration-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-green-600 rounded-full flex items-center justify-center text-3xl group-hover:scale-110 transition-transform\",\n                                                                children: \"\\uD83C\\uDDEE\\uD83C\\uDDF9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-2xl font-bold text-green-400\",\n                                                                        children: \"ITALIA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 315,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-300\",\n                                                                        children: \"Raffinatezza e Design Elegante\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mediterranean-map\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-br from-blue-600/30 to-cyan-400/30 rounded-3xl backdrop-blur-sm border border-cyan-400/30\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 p-12 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-6xl mb-6 animate-bounce-slow\",\n                                                            children: \"\\uD83C\\uDF0A\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-3xl font-bold text-cyan-400 mb-4\",\n                                                            children: \"MEDITERRANEO\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 text-lg leading-relaxed\",\n                                                            children: \"Il nostro mare, la nostra ispirazione, la nostra energia che unisce due culture in una sola esperienza.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-8 flex justify-center space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 bg-cyan-400 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 bg-cyan-400 rounded-full animate-pulse delay-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 bg-cyan-400 rounded-full animate-pulse delay-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"acquista\",\n                className: \"py-32 bg-gradient-to-b from-gray-900 to-black relative overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-5xl md:text-7xl font-black mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-gradient-6\",\n                                        children: \"SCEGLI LA TUA\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-yellow-400 text-shadow-glow\",\n                                        children: \"DOSE DI ENERGIA\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8 mb-16\",\n                            children: [\n                                {\n                                    quantity: \"1 LATTINA\",\n                                    price: \"2.50\",\n                                    subtitle: \"Prova Singola\",\n                                    popular: false\n                                },\n                                {\n                                    quantity: \"6 LATTINE\",\n                                    price: \"14.00\",\n                                    subtitle: \"Pack Famiglia\",\n                                    popular: true\n                                },\n                                {\n                                    quantity: \"12 LATTINE\",\n                                    price: \"26.00\",\n                                    subtitle: \"Scorta Energia\",\n                                    popular: false\n                                }\n                            ].map((pack, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group relative pricing-card transform-gpu \".concat(pack.popular ? \"popular-card\" : \"\"),\n                                    style: {\n                                        animationDelay: \"\".concat(index * 200, \"ms\")\n                                    },\n                                    children: [\n                                        pack.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-6 left-1/2 transform -translate-x-1/2 z-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-yellow-400 to-yellow-600 text-black px-6 py-3 rounded-full text-sm font-black tracking-wider animate-pulse\",\n                                                children: \"⚡ PI\\xd9 POPOLARE ⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-3xl backdrop-blur-sm border border-yellow-400/20 group-hover:border-yellow-400/60 transition-all duration-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, this),\n                                        pack.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-3xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10 p-10 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-yellow-400 mb-2\",\n                                                            children: pack.quantity\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: pack.subtitle\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-5xl font-black mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: pack.price\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-yellow-400 text-2xl ml-2\",\n                                                                children: \"TND\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full bg-gradient-to-r from-yellow-400 to-yellow-600 text-black py-4 rounded-2xl font-bold text-lg hover:shadow-2xl hover:shadow-yellow-400/50 transition-all duration-500 transform hover:scale-105 group-hover:-rotate-1\",\n                                                    children: \"ORDINA ORA ⚡\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center space-x-4 bg-gradient-to-r from-yellow-400/20 to-transparent p-6 rounded-full border border-yellow-400/30\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-400 font-bold text-xl\",\n                                        children: \"\\uD83D\\uDE9A Spedizione in tutta la Tunisia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"group bg-transparent border-2 border-yellow-400 text-yellow-400 px-10 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-black transition-all duration-500 transform hover:scale-105\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            \"\\uD83D\\uDCCD TROVA IL PUNTO VENDITA PI\\xd9 VICINO\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-3 group-hover:translate-x-2 transition-transform\",\n                                                children: \"→\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 346,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-32 bg-gradient-to-t from-black to-gray-900 relative overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-5xl md:text-7xl font-black mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-gradient-7\",\n                                        children: \"CONTATTACI O\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-yellow-400 text-shadow-glow\",\n                                        children: \"DIVENTA PARTNER\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-2 gap-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        {\n                                            icon: \"\\uD83D\\uDCE7\",\n                                            title: \"EMAIL\",\n                                            info: \"<EMAIL>\"\n                                        },\n                                        {\n                                            icon: \"\\uD83D\\uDCF1\",\n                                            title: \"TELEFONO\",\n                                            info: \"+216 XX XXX XXX\"\n                                        },\n                                        {\n                                            icon: \"\\uD83D\\uDCCD\",\n                                            title: \"SEDE\",\n                                            info: \"Tunis, Tunisia\"\n                                        }\n                                    ].map((contact, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group flex items-center space-x-6 p-6 bg-gradient-to-r from-yellow-400/10 to-transparent rounded-2xl border border-yellow-400/30 hover:border-yellow-400/60 transition-all duration-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center text-2xl text-black group-hover:scale-110 transition-transform\",\n                                                    children: contact.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-yellow-400\",\n                                                            children: contact.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 text-lg\",\n                                                            children: contact.info\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-gray-800/50 to-gray-900/50 p-10 rounded-3xl border border-yellow-400/30 backdrop-blur-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-3xl font-bold text-yellow-400 mb-8 text-center\",\n                                            children: \"DIVENTA PARTNER\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Nome Attivit\\xe0\",\n                                                    className: \"w-full bg-gray-700/50 border border-gray-600 rounded-2xl px-6 py-4 text-white placeholder-gray-400 focus:border-yellow-400 focus:outline-none transition-all duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    placeholder: \"Email\",\n                                                    className: \"w-full bg-gray-700/50 border border-gray-600 rounded-2xl px-6 py-4 text-white placeholder-gray-400 focus:border-yellow-400 focus:outline-none transition-all duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    placeholder: \"Messaggio\",\n                                                    rows: 4,\n                                                    className: \"w-full bg-gray-700/50 border border-gray-600 rounded-2xl px-6 py-4 text-white placeholder-gray-400 focus:border-yellow-400 focus:outline-none resize-none transition-all duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    className: \"w-full bg-gradient-to-r from-yellow-400 to-yellow-600 text-black py-4 rounded-2xl font-bold text-lg hover:shadow-2xl hover:shadow-yellow-400/50 transition-all duration-500 transform hover:scale-105\",\n                                                    children: \"INVIA RICHIESTA ⚡\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 416,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-black border-t border-yellow-400/30 py-12 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"footer-pattern opacity-10\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-6 text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-black text-yellow-400 mb-4 glitch-text\",\n                                        children: \"ȺFⱤƗȻ\\xd8ŁȺ ENERGY\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-xl mb-6\",\n                                        children: \"L'energia che nasce dal Mediterraneo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center space-x-8 text-gray-400 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\xa9 2024 AfriCola Energy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-yellow-400\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Made in Tunisia \\uD83C\\uDDF9\\uD83C\\uDDF3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-yellow-400\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Designed with Italian Passion \\uD83C\\uDDEE\\uD83C\\uDDF9\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-yellow-400 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-yellow-400 rounded-full animate-pulse delay-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-yellow-400 rounded-full animate-pulse delay-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 478,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_s(AfriColaWebsite, \"AQtXeVPeaUFLLPSV2Z7vl1d+38w=\");\n_c = AfriColaWebsite;\nvar _c;\n$RefreshReg$(_c, \"AfriColaWebsite\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFbUQ7QUFFcEMsU0FBU0c7O0lBQ3RCLE1BQU1DLFVBQVVILDZDQUFNQSxDQUFjO0lBQ3BDLE1BQU1JLFNBQVNKLDZDQUFNQSxDQUFpQjtJQUN0QyxNQUFNLENBQUNLLGVBQWVDLGlCQUFpQixHQUFHTCwrQ0FBUUEsQ0FBQztRQUFFTSxHQUFHO1FBQUdDLEdBQUc7SUFBRTtJQUNoRSxNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR1QsK0NBQVFBLENBQUM7SUFFdkNGLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTVksa0JBQWtCLENBQUNDO1lBQ3ZCTixpQkFBaUI7Z0JBQUVDLEdBQUdLLEVBQUVDLE9BQU87Z0JBQUVMLEdBQUdJLEVBQUVFLE9BQU87WUFBQztRQUNoRDtRQUVBLE1BQU1DLGVBQWU7WUFDbkJMLFdBQVdNLE9BQU9QLE9BQU87WUFFekIsc0JBQXNCO1lBQ3RCLElBQUlMLE9BQU9hLE9BQU8sRUFBRTtnQkFDbEIsTUFBTUMsV0FBV0YsT0FBT0csV0FBVztnQkFDbkMsTUFBTUMsT0FBT0YsV0FBVyxDQUFDO2dCQUN6QixNQUFNRyxTQUFTSCxXQUFXO2dCQUMxQmQsT0FBT2EsT0FBTyxDQUFDSyxLQUFLLENBQUNDLFNBQVMsR0FBRywwQkFFckJGLE9BREdELE1BQUssNEJBRVJDLE9BREFBLFFBQU8sNkJBRVQsT0FERUEsU0FBUyxLQUFJLDBCQUNPLE9BQXRCLElBQUlILFdBQVcsUUFBTztZQUVsQztRQUNGO1FBRUFGLE9BQU9RLGdCQUFnQixDQUFDLGFBQWFiO1FBQ3JDSyxPQUFPUSxnQkFBZ0IsQ0FBQyxVQUFVVDtRQUVsQyxPQUFPO1lBQ0xDLE9BQU9TLG1CQUFtQixDQUFDLGFBQWFkO1lBQ3hDSyxPQUFPUyxtQkFBbUIsQ0FBQyxVQUFVVjtRQUN2QztJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU1XLGtCQUFrQixDQUFDQztZQUN2QkM7U0FBQUEsMkJBQUFBLFNBQVNDLGNBQWMsQ0FBQ0Ysd0JBQXhCQywrQ0FBQUEseUJBQW9DRSxjQUFjLENBQUM7WUFBRUMsVUFBVTtRQUFTO0lBQzFFO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDWjt1QkFBSUMsTUFBTTtpQkFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQ0MsR0FBR0Msa0JBQ3RCLDhEQUFDTDt3QkFFQ0MsV0FBVTt3QkFDVlgsT0FBTzs0QkFDTGdCLE1BQU0sR0FBdUIsT0FBcEJDLEtBQUtDLE1BQU0sS0FBSyxLQUFJOzRCQUM3QkMsS0FBSyxHQUF1QixPQUFwQkYsS0FBS0MsTUFBTSxLQUFLLEtBQUk7NEJBQzVCRSxnQkFBZ0IsR0FBcUIsT0FBbEJILEtBQUtDLE1BQU0sS0FBSyxHQUFFOzRCQUNyQ0csbUJBQW1CLEdBQXlCLE9BQXRCLElBQUlKLEtBQUtDLE1BQU0sS0FBSyxHQUFFO3dCQUM5Qzt1QkFQS0g7Ozs7Ozs7Ozs7MEJBYVgsOERBQUNMO2dCQUNDQyxXQUFVO2dCQUNWWCxPQUFPO29CQUNMZ0IsTUFBTWpDLGNBQWNFLENBQUMsR0FBRztvQkFDeEJrQyxLQUFLcEMsY0FBY0csQ0FBQyxHQUFHO29CQUN2QmUsV0FBVyxTQUFnRCxPQUF2QyxJQUFJZ0IsS0FBS0ssR0FBRyxDQUFDQyxLQUFLQyxHQUFHLEtBQUssU0FBUyxLQUFJO2dCQUM3RDs7Ozs7OzBCQUlGLDhEQUFDQztnQkFBSWQsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQWlFOzs7Ozs7MENBQ2hGLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDWjtvQ0FBQztvQ0FBUTtvQ0FBWTtvQ0FBWTtpQ0FBVyxDQUFDRSxHQUFHLENBQUMsQ0FBQ2EsTUFBTVgsa0JBQ3ZELDhEQUFDWTt3Q0FFQ0MsU0FBUyxJQUFNeEIsZ0JBQWdCc0IsS0FBS0csV0FBVyxHQUFHQyxPQUFPLENBQUMsUUFBSzt3Q0FDL0RuQixXQUFVOzswREFFViw4REFBQ29CO2dEQUFLcEIsV0FBVTswREFBaUJlOzs7Ozs7MERBQ2pDLDhEQUFDaEI7Z0RBQUlDLFdBQVU7Ozs7Ozs7dUNBTFZlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFjakIsOERBQUNNO2dCQUNDQyxJQUFHO2dCQUNIQyxLQUFLckQ7Z0JBQ0w4QixXQUFVOztrQ0FHViw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs7Ozs7Ozs7OztrQ0FJakIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7Ozs7OzswQ0FDZiw4REFBQ0Q7Z0NBQUlDLFdBQVU7Ozs7OzswQ0FDZiw4REFBQ0Q7Z0NBQUlDLFdBQVU7Ozs7Ozs7Ozs7OztrQ0FJakIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7Ozs7OzswQ0FDZiw4REFBQ0Q7Z0NBQUlDLFdBQVU7Ozs7OzswQ0FDZiw4REFBQ0Q7Z0NBQUlDLFdBQVU7Ozs7Ozs7Ozs7OztrQ0FHakIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUF3RTs7Ozs7OzBEQUd2Riw4REFBQ3dCO2dEQUFHeEIsV0FBVTs7a0VBQ1osOERBQUNvQjt3REFBS3BCLFdBQVU7a0VBQTBDOzs7Ozs7a0VBQzFELDhEQUFDb0I7d0RBQUtwQixXQUFVO2tFQUFvRDs7Ozs7O2tFQUNwRSw4REFBQ29CO3dEQUFLcEIsV0FBVTtrRUFBcUU7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFNekYsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ3lCO2dEQUFFekIsV0FBVTs7b0RBQStEO2tFQUUxRSw4REFBQ29CO3dEQUFLcEIsV0FBVTtrRUFBMEM7Ozs7OztvREFBaUI7Ozs7Ozs7MERBRTdFLDhEQUFDeUI7Z0RBQUV6QixXQUFVOzBEQUF3Qzs7Ozs7Ozs7Ozs7O2tEQU12RCw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDZ0I7Z0RBQU9oQixXQUFVOztrRUFDaEIsOERBQUNvQjt3REFBS3BCLFdBQVU7OzREQUFrQzswRUFFaEQsOERBQUNvQjtnRUFBS3BCLFdBQVU7MEVBQStEOzs7Ozs7Ozs7Ozs7a0VBRWpGLDhEQUFDRDt3REFBSUMsV0FBVTs7Ozs7Ozs7Ozs7OzBEQUdqQiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTs7Ozs7O2tFQUNmLDhEQUFDeUI7d0RBQUV6QixXQUFVO2tFQUFtRDs7Ozs7O2tFQUNoRSw4REFBQ0Q7d0RBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FNckIsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUl3QixLQUFLcEQ7d0NBQVE2QixXQUFVOzswREFFMUIsOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7MERBR2YsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7Ozs7OztrRUFDZiw4REFBQ0Q7d0RBQUlDLFdBQVU7Ozs7OztrRUFDZiw4REFBQ0Q7d0RBQUlDLFdBQVU7Ozs7Ozs7Ozs7OzswREFJakIsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDMEI7b0RBQ0NDLEtBQUk7b0RBQ0pDLEtBQUk7b0RBQ0o1QixXQUFVO29EQUNWWCxPQUFPO3dEQUNMd0MsUUFBUTtvREFDVjs7Ozs7Ozs7Ozs7MERBS0osOERBQUM5QjtnREFBSUMsV0FBVTs7Ozs7OzBEQUNmLDhEQUFDRDtnREFBSUMsV0FBVTs7Ozs7OzBEQUNmLDhEQUFDRDtnREFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPdkIsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTXJCLDhEQUFDcUI7Z0JBQ0NDLElBQUc7Z0JBQ0h0QixXQUFVOztrQ0FHViw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs7Ozs7Ozs7OztrQ0FHakIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ29COzRDQUFLcEIsV0FBVTtzREFBa0g7Ozs7Ozs7Ozs7O2tEQUlwSSw4REFBQzhCO3dDQUFHOUIsV0FBVTs7MERBQ1osOERBQUNvQjtnREFBS3BCLFdBQVU7MERBQXdCOzs7Ozs7MERBQ3hDLDhEQUFDb0I7Z0RBQUtwQixXQUFVOzBEQUF5Qzs7Ozs7OzBEQUN6RCw4REFBQ29CO2dEQUFLcEIsV0FBVTswREFBd0I7Ozs7Ozs7Ozs7OztrREFFMUMsOERBQUN5Qjt3Q0FBRXpCLFdBQVU7a0RBQTBEOzs7Ozs7Ozs7Ozs7MENBTXpFLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDWjtvQ0FDQzt3Q0FBRStCLE9BQU87d0NBQXlCQyxNQUFNO3dDQUFLQyxPQUFPO29DQUFnQztvQ0FDcEY7d0NBQUVGLE9BQU87d0NBQXFDQyxNQUFNO3dDQUFNQyxPQUFPO29DQUErQjtvQ0FDaEc7d0NBQUVGLE9BQU87d0NBQXFCQyxNQUFNO3dDQUFNQyxPQUFPO29DQUE2QjtvQ0FDOUU7d0NBQUVGLE9BQU87d0NBQXVCQyxNQUFNO3dDQUFRQyxPQUFPO29DQUE2QjtvQ0FDbEY7d0NBQUVGLE9BQU87d0NBQW1CQyxNQUFNO3dDQUFNQyxPQUFPO29DQUErQjtpQ0FDL0UsQ0FBQy9CLEdBQUcsQ0FBQyxDQUFDZ0MsU0FBU0Msc0JBQ2QsOERBQUNwQzt3Q0FFQ0MsV0FBVTt3Q0FDVlgsT0FBTzs0Q0FBRW9CLGdCQUFnQixHQUFlLE9BQVowQixRQUFRLEtBQUk7d0NBQUk7OzBEQUU1Qyw4REFBQ3BDO2dEQUFJQyxXQUFVOzs7Ozs7MERBQ2YsOERBQUNEO2dEQUNDQyxXQUFXLHNDQUFvRCxPQUFka0MsUUFBUUQsS0FBSyxFQUFDOzs7Ozs7MERBR2pFLDhEQUFDbEM7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDWmtDLFFBQVFGLElBQUk7Ozs7OztrRUFFZiw4REFBQ0k7d0RBQUdwQyxXQUFVO2tFQUF3RGtDLFFBQVFILEtBQUs7Ozs7OztrRUFDbkYsOERBQUNoQzt3REFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozt1Q0FkWm1DOzs7Ozs7Ozs7OzBDQXFCWCw4REFBQ3BDO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs7Ozs7c0RBQ2YsOERBQUNvQjs0Q0FBS3BCLFdBQVU7c0RBQTJDOzs7Ozs7c0RBQzNELDhEQUFDRDs0Q0FBSUMsV0FBVTs7Ozs7O3NEQUNmLDhEQUFDb0I7NENBQUtwQixXQUFVO3NEQUEyQzs7Ozs7O3NEQUMzRCw4REFBQ0Q7NENBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU92Qiw4REFBQ3FCO2dCQUFRQyxJQUFHO2dCQUFXdEIsV0FBVTs7a0NBRS9CLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7Ozs7Ozs7Ozs7O2tDQUdqQiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQzhCO29DQUFHOUIsV0FBVTs7c0RBQ1osOERBQUNvQjs0Q0FBS3BCLFdBQVU7c0RBQXdCOzs7Ozs7c0RBQ3hDLDhEQUFDb0I7NENBQUtwQixXQUFVO3NEQUF5Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSTdELDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ3lCO2dEQUFFekIsV0FBVTswREFBd0M7Ozs7OzswREFNckQsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTswRUFBeUg7Ozs7OzswRUFHeEksOERBQUNEOztrRkFDQyw4REFBQ3FDO3dFQUFHcEMsV0FBVTtrRkFBa0M7Ozs7OztrRkFDaEQsOERBQUN5Qjt3RUFBRXpCLFdBQVU7a0ZBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBSWpDLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOzBFQUEySDs7Ozs7OzBFQUcxSSw4REFBQ0Q7O2tGQUNDLDhEQUFDcUM7d0VBQUdwQyxXQUFVO2tGQUFvQzs7Ozs7O2tGQUNsRCw4REFBQ3lCO3dFQUFFekIsV0FBVTtrRkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFPckMsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzs7Ozs7OERBQ2YsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7c0VBQW9DOzs7Ozs7c0VBQ25ELDhEQUFDb0M7NERBQUdwQyxXQUFVO3NFQUF3Qzs7Ozs7O3NFQUN0RCw4REFBQ3lCOzREQUFFekIsV0FBVTtzRUFBd0M7Ozs7OztzRUFJckQsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7Ozs7Ozs4RUFDZiw4REFBQ0Q7b0VBQUlDLFdBQVU7Ozs7Ozs4RUFDZiw4REFBQ0Q7b0VBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVU3Qiw4REFBQ3FCO2dCQUFRQyxJQUFHO2dCQUFXdEIsV0FBVTswQkFDL0IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUM4QjtnQ0FBRzlCLFdBQVU7O2tEQUNaLDhEQUFDb0I7d0NBQUtwQixXQUFVO2tEQUF3Qjs7Ozs7O2tEQUN4Qyw4REFBQ29CO3dDQUFLcEIsV0FBVTtrREFBeUM7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUk3RCw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ1o7Z0NBQ0M7b0NBQUVxQyxVQUFVO29DQUFhQyxPQUFPO29DQUFRQyxVQUFVO29DQUFpQkMsU0FBUztnQ0FBTTtnQ0FDbEY7b0NBQUVILFVBQVU7b0NBQWFDLE9BQU87b0NBQVNDLFVBQVU7b0NBQWlCQyxTQUFTO2dDQUFLO2dDQUNsRjtvQ0FBRUgsVUFBVTtvQ0FBY0MsT0FBTztvQ0FBU0MsVUFBVTtvQ0FBa0JDLFNBQVM7Z0NBQU07NkJBQ3RGLENBQUN0QyxHQUFHLENBQUMsQ0FBQ3VDLE1BQU1OLHNCQUNYLDhEQUFDcEM7b0NBRUNDLFdBQVcsNkNBQWdGLE9BQW5DeUMsS0FBS0QsT0FBTyxHQUFHLGlCQUFpQjtvQ0FDeEZuRCxPQUFPO3dDQUFFb0IsZ0JBQWdCLEdBQWUsT0FBWjBCLFFBQVEsS0FBSTtvQ0FBSTs7d0NBRTNDTSxLQUFLRCxPQUFPLGtCQUNYLDhEQUFDekM7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNEO2dEQUFJQyxXQUFVOzBEQUFtSTs7Ozs7Ozs7Ozs7c0RBTXRKLDhEQUFDRDs0Q0FBSUMsV0FBVTs7Ozs7O3dDQUNkeUMsS0FBS0QsT0FBTyxrQkFDWCw4REFBQ3pDOzRDQUFJQyxXQUFVOzs7Ozs7c0RBR2pCLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ29DOzREQUFHcEMsV0FBVTtzRUFBMkN5QyxLQUFLSixRQUFROzs7Ozs7c0VBQ3RFLDhEQUFDWjs0REFBRXpCLFdBQVU7c0VBQXlCeUMsS0FBS0YsUUFBUTs7Ozs7Ozs7Ozs7OzhEQUdyRCw4REFBQ3hDO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNvQjtnRUFBS3BCLFdBQVU7MEVBQWN5QyxLQUFLSCxLQUFLOzs7Ozs7MEVBQ3hDLDhEQUFDbEI7Z0VBQUtwQixXQUFVOzBFQUFnQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSXBELDhEQUFDZ0I7b0RBQU9oQixXQUFVOzhEQUE4Tjs7Ozs7Ozs7Ozs7OzttQ0E5QjdPbUM7Ozs7Ozs7Ozs7c0NBc0NYLDhEQUFDcEM7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ29CO3dDQUFLcEIsV0FBVTtrREFBb0M7Ozs7Ozs7Ozs7OzhDQUd0RCw4REFBQ2dCO29DQUFPaEIsV0FBVTs4Q0FDaEIsNEVBQUNvQjt3Q0FBS3BCLFdBQVU7OzRDQUFvQjswREFFbEMsOERBQUNvQjtnREFBS3BCLFdBQVU7MERBQXNEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFoRiw4REFBQ3FCO2dCQUFRckIsV0FBVTswQkFDakIsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUM4QjtnQ0FBRzlCLFdBQVU7O2tEQUNaLDhEQUFDb0I7d0NBQUtwQixXQUFVO2tEQUF3Qjs7Ozs7O2tEQUN4Qyw4REFBQ29CO3dDQUFLcEIsV0FBVTtrREFBeUM7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUk3RCw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWjt3Q0FDQzs0Q0FBRWdDLE1BQU07NENBQU1ELE9BQU87NENBQVNXLE1BQU07d0NBQTBCO3dDQUM5RDs0Q0FBRVYsTUFBTTs0Q0FBTUQsT0FBTzs0Q0FBWVcsTUFBTTt3Q0FBa0I7d0NBQ3pEOzRDQUFFVixNQUFNOzRDQUFNRCxPQUFPOzRDQUFRVyxNQUFNO3dDQUFpQjtxQ0FDckQsQ0FBQ3hDLEdBQUcsQ0FBQyxDQUFDeUMsU0FBU1Isc0JBQ2QsOERBQUNwQzs0Q0FFQ0MsV0FBVTs7OERBRVYsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNaMkMsUUFBUVgsSUFBSTs7Ozs7OzhEQUVmLDhEQUFDakM7O3NFQUNDLDhEQUFDcUM7NERBQUdwQyxXQUFVO3NFQUFxQzJDLFFBQVFaLEtBQUs7Ozs7OztzRUFDaEUsOERBQUNOOzREQUFFekIsV0FBVTtzRUFBeUIyQyxRQUFRRCxJQUFJOzs7Ozs7Ozs7Ozs7OzJDQVIvQ1A7Ozs7Ozs7Ozs7OENBY1gsOERBQUNwQztvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNvQzs0Q0FBR3BDLFdBQVU7c0RBQXNEOzs7Ozs7c0RBQ3BFLDhEQUFDNEM7NENBQUs1QyxXQUFVOzs4REFDZCw4REFBQzZDO29EQUNDQyxNQUFLO29EQUNMQyxhQUFZO29EQUNaL0MsV0FBVTs7Ozs7OzhEQUVaLDhEQUFDNkM7b0RBQ0NDLE1BQUs7b0RBQ0xDLGFBQVk7b0RBQ1ovQyxXQUFVOzs7Ozs7OERBRVosOERBQUNnRDtvREFDQ0QsYUFBWTtvREFDWkUsTUFBTTtvREFDTmpELFdBQVU7Ozs7Ozs4REFFWiw4REFBQ2dCO29EQUNDOEIsTUFBSztvREFDTDlDLFdBQVU7OERBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVVYLDhEQUFDa0Q7Z0JBQU9sRCxXQUFVOztrQ0FDaEIsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7a0NBR2pCLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQXVEOzs7Ozs7a0RBQ3RFLDhEQUFDeUI7d0NBQUV6QixXQUFVO2tEQUE2Qjs7Ozs7O2tEQUUxQyw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDb0I7MERBQUs7Ozs7OzswREFDTiw4REFBQ0E7Z0RBQUtwQixXQUFVOzBEQUFrQjs7Ozs7OzBEQUNsQyw4REFBQ29COzBEQUFLOzs7Ozs7MERBQ04sOERBQUNBO2dEQUFLcEIsV0FBVTswREFBa0I7Ozs7OzswREFDbEMsOERBQUNvQjswREFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUlWLDhEQUFDckI7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7Ozs7O2tEQUNmLDhEQUFDRDt3Q0FBSUMsV0FBVTs7Ozs7O2tEQUNmLDhEQUFDRDt3Q0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTTNCO0dBcmZ3Qi9CO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC9wYWdlLnRzeD83NjAzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXHJcblxyXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIlxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQWZyaUNvbGFXZWJzaXRlKCkge1xyXG4gIGNvbnN0IGhlcm9SZWYgPSB1c2VSZWY8SFRNTEVsZW1lbnQ+KG51bGwpXHJcbiAgY29uc3QgY2FuUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKVxyXG4gIGNvbnN0IFttb3VzZVBvc2l0aW9uLCBzZXRNb3VzZVBvc2l0aW9uXSA9IHVzZVN0YXRlKHsgeDogMCwgeTogMCB9KVxyXG4gIGNvbnN0IFtzY3JvbGxZLCBzZXRTY3JvbGxZXSA9IHVzZVN0YXRlKDApXHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBoYW5kbGVNb3VzZU1vdmUgPSAoZTogTW91c2VFdmVudCkgPT4ge1xyXG4gICAgICBzZXRNb3VzZVBvc2l0aW9uKHsgeDogZS5jbGllbnRYLCB5OiBlLmNsaWVudFkgfSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBoYW5kbGVTY3JvbGwgPSAoKSA9PiB7XHJcbiAgICAgIHNldFNjcm9sbFkod2luZG93LnNjcm9sbFkpXHJcblxyXG4gICAgICAvLyAzRCBQYXJhbGxheCBmb3IgY2FuXHJcbiAgICAgIGlmIChjYW5SZWYuY3VycmVudCkge1xyXG4gICAgICAgIGNvbnN0IHNjcm9sbGVkID0gd2luZG93LnBhZ2VZT2Zmc2V0XHJcbiAgICAgICAgY29uc3QgcmF0ZSA9IHNjcm9sbGVkICogLTAuM1xyXG4gICAgICAgIGNvbnN0IHJvdGF0ZSA9IHNjcm9sbGVkICogMC4yXHJcbiAgICAgICAgY2FuUmVmLmN1cnJlbnQuc3R5bGUudHJhbnNmb3JtID0gYFxyXG4gICAgICAgICAgdHJhbnNsYXRlWSgke3JhdGV9cHgpIFxyXG4gICAgICAgICAgcm90YXRlWCgke3JvdGF0ZX1kZWcpIFxyXG4gICAgICAgICAgcm90YXRlWSgke3JvdGF0ZSAqIDAuNX1kZWcpXHJcbiAgICAgICAgICBzY2FsZSgkezEgKyBzY3JvbGxlZCAqIDAuMDAwNX0pXHJcbiAgICAgICAgYFxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJtb3VzZW1vdmVcIiwgaGFuZGxlTW91c2VNb3ZlKVxyXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJzY3JvbGxcIiwgaGFuZGxlU2Nyb2xsKVxyXG5cclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKFwibW91c2Vtb3ZlXCIsIGhhbmRsZU1vdXNlTW92ZSlcclxuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJzY3JvbGxcIiwgaGFuZGxlU2Nyb2xsKVxyXG4gICAgfVxyXG4gIH0sIFtdKVxyXG5cclxuICBjb25zdCBzY3JvbGxUb1NlY3Rpb24gPSAoc2VjdGlvbklkOiBzdHJpbmcpID0+IHtcclxuICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKHNlY3Rpb25JZCk/LnNjcm9sbEludG9WaWV3KHsgYmVoYXZpb3I6IFwic21vb3RoXCIgfSlcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ibGFjayB0ZXh0LXdoaXRlIG92ZXJmbG93LXgtaGlkZGVuIHJlbGF0aXZlXCI+XHJcbiAgICAgIHsvKiBGbG9hdGluZyBQYXJ0aWNsZXMgKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBwb2ludGVyLWV2ZW50cy1ub25lIHotMFwiPlxyXG4gICAgICAgIHtbLi4uQXJyYXkoNTApXS5tYXAoKF8sIGkpID0+IChcclxuICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAga2V5PXtpfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB3LTEgaC0xIGJnLXllbGxvdy00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtZmxvYXRcIlxyXG4gICAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICAgIGxlZnQ6IGAke01hdGgucmFuZG9tKCkgKiAxMDB9JWAsXHJcbiAgICAgICAgICAgICAgdG9wOiBgJHtNYXRoLnJhbmRvbSgpICogMTAwfSVgLFxyXG4gICAgICAgICAgICAgIGFuaW1hdGlvbkRlbGF5OiBgJHtNYXRoLnJhbmRvbSgpICogNX1zYCxcclxuICAgICAgICAgICAgICBhbmltYXRpb25EdXJhdGlvbjogYCR7MyArIE1hdGgucmFuZG9tKCkgKiA0fXNgLFxyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICApKX1cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogTW91c2UgRm9sbG93ZXIgKi99XHJcbiAgICAgIDxkaXZcclxuICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCB3LTggaC04IGJnLXllbGxvdy00MDAvMzAgcm91bmRlZC1mdWxsIHBvaW50ZXItZXZlbnRzLW5vbmUgei01MCBtaXgtYmxlbmQtZGlmZmVyZW5jZSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZWFzZS1vdXRcIlxyXG4gICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICBsZWZ0OiBtb3VzZVBvc2l0aW9uLnggLSAxNixcclxuICAgICAgICAgIHRvcDogbW91c2VQb3NpdGlvbi55IC0gMTYsXHJcbiAgICAgICAgICB0cmFuc2Zvcm06IGBzY2FsZSgkezEgKyBNYXRoLnNpbihEYXRlLm5vdygpICogMC4wMDUpICogMC4zfSlgLFxyXG4gICAgICAgIH19XHJcbiAgICAgIC8+XHJcblxyXG4gICAgICB7LyogRnV0dXJpc3RpYyBOYXZpZ2F0aW9uICovfVxyXG4gICAgICA8bmF2IGNsYXNzTmFtZT1cImZpeGVkIHRvcC0wIHctZnVsbCB6LTUwIGJnLWJsYWNrLzIwIGJhY2tkcm9wLWJsdXIteGwgYm9yZGVyLWIgYm9yZGVyLXllbGxvdy00MDAvMzBcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTYgcHktNFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJsYWNrIHRleHQteWVsbG93LTQwMCB0cmFja2luZy13aWRlciBnbGl0Y2gtdGV4dFwiPsi6RuKxpMaXyLvDmMWByLo8L2Rpdj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6ZmxleCBzcGFjZS14LThcIj5cclxuICAgICAgICAgICAgICB7W1wiSE9NRVwiLCBcIlBST0RPVFRPXCIsIFwiSURFTlRJVMOAXCIsIFwiQUNRVUlTVEFcIl0ubWFwKChpdGVtLCBpKSA9PiAoXHJcbiAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIGtleT17aXRlbX1cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2Nyb2xsVG9TZWN0aW9uKGl0ZW0udG9Mb3dlckNhc2UoKS5yZXBsYWNlKFwiw6BcIiwgXCJhXCIpKX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgZ3JvdXAgcHgtNCBweS0yIHRleHQtc20gZm9udC1ib2xkIHRyYWNraW5nLXdpZGVyIGhvdmVyOnRleHQteWVsbG93LTQwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwXCI+e2l0ZW19PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmcteWVsbG93LTQwMC8yMCBzY2FsZS14LTAgZ3JvdXAtaG92ZXI6c2NhbGUteC0xMDAgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwIG9yaWdpbi1sZWZ0IHJvdW5kZWQtZnVsbFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9uYXY+XHJcblxyXG4gICAgICB7LyogSU5TQU5FIEhFUk8gU0VDVElPTiAqL31cclxuICAgICAgPHNlY3Rpb25cclxuICAgICAgICBpZD1cImhvbWVcIlxyXG4gICAgICAgIHJlZj17aGVyb1JlZn1cclxuICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSBtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgb3ZlcmZsb3ctaGlkZGVuXCJcclxuICAgICAgPlxyXG4gICAgICAgIHsvKiBBbmltYXRlZCBCYWNrZ3JvdW5kIEdyaWQgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIG9wYWNpdHktMjBcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZC1iYWNrZ3JvdW5kXCIgLz5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgey8qIE1vcnBoaW5nIEJsb2JzICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJibG9iIGJsb2ItMVwiIC8+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJsb2IgYmxvYi0yXCIgLz5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmxvYiBibG9iLTNcIiAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogTGlnaHRuaW5nIEVmZmVjdHMgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxpZ2h0bmluZyBsaWdodG5pbmctMVwiIC8+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxpZ2h0bmluZyBsaWdodG5pbmctMlwiIC8+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxpZ2h0bmluZyBsaWdodG5pbmctM1wiIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBtYXgtdy03eGwgbXgtYXV0byBweC02IGdyaWQgbGc6Z3JpZC1jb2xzLTIgZ2FwLTEyIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBsZzp0ZXh0LWxlZnRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy00MDAgdGV4dC14bCBmb250LWJvbGQgbWItNCB0cmFja2luZy1bMC4zZW1dIGFuaW1hdGUtcHVsc2VcIj5cclxuICAgICAgICAgICAgICAgIOKaoSBNRURJVEVSUkFORUFOIEVORVJHWSDimqFcclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC02eGwgbWQ6dGV4dC04eGwgZm9udC1ibGFjayBtYi02IGxlYWRpbmctbm9uZVwiPlxyXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1ncmFkaWVudC0xIGFuaW1hdGUtdGV4dC1nbG93XCI+TCdFTkVSR0lBPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1ncmFkaWVudC0yIGFuaW1hdGUtdGV4dC1nbG93IGRlbGF5LTMwMFwiPkNIRSBOQVNDRTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJsb2NrIHRleHQteWVsbG93LTQwMCBhbmltYXRlLXRleHQtZ2xvdyBkZWxheS02MDAgdGV4dC1zaGFkb3ctZ2xvd1wiPlxyXG4gICAgICAgICAgICAgICAgICBEQUwgTUVESVRFUlJBTkVPXHJcbiAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgPC9oMT5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNiBtYi04XCI+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCBtZDp0ZXh0LTJ4bCB0ZXh0LWdyYXktMzAwIGxlYWRpbmctcmVsYXhlZCBmb250LWxpZ2h0XCI+XHJcbiAgICAgICAgICAgICAgICBBZnJpQ29sYSBFbmVyZ3kgbm9uIMOoIHVuYSBzZW1wbGljZSBiZXZhbmRhOiDDqCB1bidcclxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteWVsbG93LTQwMCBmb250LWJvbGQgYW5pbWF0ZS1wdWxzZVwiPmVzcGVyaWVuemE8L3NwYW4+LlxyXG4gICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS00MDAgbGVhZGluZy1yZWxheGVkXCI+XHJcbiAgICAgICAgICAgICAgICBDcmVhdGEgaW4gVHVuaXNpYSBlIGlzcGlyYXRhIGRhbCBkZXNpZ24gaXRhbGlhbm8sIHF1ZXN0YSBsYXR0aW5hIGdpYWxsYSBzcHJpZ2lvbmEgdW5hIGNhcmljYSBpbnRlbnNhXHJcbiAgICAgICAgICAgICAgICBncmF6aWUgYWxsJ2VzdHJhdHRvIG5hdHVyYWxlIGRpIG5vY2UgZGkgY29sYS5cclxuICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cclxuICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cImdyb3VwIHJlbGF0aXZlIG92ZXJmbG93LWhpZGRlbiBiZy1ncmFkaWVudC10by1yIGZyb20teWVsbG93LTQwMCB0by15ZWxsb3ctNjAwIHRleHQtYmxhY2sgcHgtMTIgcHktNiB0ZXh0LXhsIGZvbnQtYmxhY2sgcm91bmRlZC1mdWxsIGhvdmVyOnNoYWRvdy0yeGwgaG92ZXI6c2hhZG93LXllbGxvdy00MDAvNTAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIHRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMTAgaG92ZXI6LXJvdGF0ZS0yXCI+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwIGZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgIFBST1ZBTEEgT1JBXHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTMgZ3JvdXAtaG92ZXI6dHJhbnNsYXRlLXgtMiB0cmFuc2l0aW9uLXRyYW5zZm9ybSB0ZXh0LTJ4bFwiPuKaoTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1yIGZyb20teWVsbG93LTYwMCB0by15ZWxsb3ctNDAwIHRyYW5zbGF0ZS14LWZ1bGwgZ3JvdXAtaG92ZXI6dHJhbnNsYXRlLXgtMCB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi01MDBcIiAvPlxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMC41IGJnLXllbGxvdy00MDAgYW5pbWF0ZS1wdWxzZVwiIC8+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy00MDAgZm9udC1ib2xkIHRleHQtbGcgdHJhY2tpbmctd2lkZXJcIj5TRU5USUxBIFRVQS4gVklWSSBBRlJJQ09MQSBFTkVSR1kuPC9wPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMC41IGJnLXllbGxvdy00MDAgYW5pbWF0ZS1wdWxzZVwiIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qIDNEIEZMT0FUSU5HIENBTiAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgZmxleCBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHBlcnNwZWN0aXZlLTEwMDBcIj5cclxuICAgICAgICAgICAgICA8ZGl2IHJlZj17Y2FuUmVmfSBjbGFzc05hbWU9XCJyZWxhdGl2ZSB0cmFuc2Zvcm0tZ3B1XCI+XHJcbiAgICAgICAgICAgICAgICB7LyogR2xvdyBSaW5nICovfVxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHJvdW5kZWQtZnVsbCBiZy1ncmFkaWVudC10by1yIGZyb20teWVsbG93LTQwMC8zMCB0by10cmFuc3BhcmVudCBhbmltYXRlLXNwaW4tc2xvdyBibHVyLXhsIHNjYWxlLTE1MFwiIC8+XHJcblxyXG4gICAgICAgICAgICAgICAgey8qIEVuZXJneSBSaW5ncyAqL31cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOTYgaC05NiBib3JkZXItMiBib3JkZXIteWVsbG93LTQwMC8zMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1waW5nXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB3LTgwIGgtODAgYm9yZGVyIGJvcmRlci15ZWxsb3ctNDAwLzIwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXBpbmcgZGVsYXktMTAwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdy02NCBoLTY0IGJvcmRlciBib3JkZXIteWVsbG93LTQwMC8xMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1waW5nIGRlbGF5LTIwMDBcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgey8qIFRoZSBDYW4gKi99XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTAgY2FuLTNkXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxpbWdcclxuICAgICAgICAgICAgICAgICAgICBzcmM9XCIvYWZyaWNvbGEtY2FuLnBuZ1wiXHJcbiAgICAgICAgICAgICAgICAgICAgYWx0PVwiQWZyaUNvbGEgRW5lcmd5IENhblwiXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy05NiBoLWF1dG8gZHJvcC1zaGFkb3ctMnhsIGhvdmVyOnNjYWxlLTExMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi03MDAgdHJhbnNmb3JtLWdwdVwiXHJcbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgICAgICAgIGZpbHRlcjogXCJkcm9wLXNoYWRvdygwIDAgMzBweCByZ2JhKDI1NSwgMjA0LCAwLCAwLjUpKVwiLFxyXG4gICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICB7LyogRmxvYXRpbmcgRWxlbWVudHMgKi99XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0xMCAtbGVmdC0xMCB3LTQgaC00IGJnLXllbGxvdy00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtYm91bmNlIGRlbGF5LTMwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0yMCAtcmlnaHQtOCB3LTMgaC0zIGJnLXllbGxvdy00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtYm91bmNlIGRlbGF5LTcwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0zMiByaWdodC0xMiB3LTIgaC0yIGJnLXllbGxvdy00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtYm91bmNlIGRlbGF5LTEwMDBcIiAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogU2Nyb2xsIEluZGljYXRvciAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS04IGxlZnQtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXgtMS8yXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNjcm9sbC1pbmRpY2F0b3JcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzY3JvbGwtd2hlZWxcIiAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvc2VjdGlvbj5cclxuXHJcbiAgICAgIHsvKiBQUk9EVUNUIFNFQ1RJT04gV0lUSCAzRCBDQVJEUyAqL31cclxuICAgICAgPHNlY3Rpb25cclxuICAgICAgICBpZD1cInByb2RvdHRvXCJcclxuICAgICAgICBjbGFzc05hbWU9XCJweS0zMiBiZy1ncmFkaWVudC10by1iIGZyb20tYmxhY2sgdmlhLWdyYXktOTAwIHRvLWJsYWNrIHJlbGF0aXZlIG92ZXJmbG93LWhpZGRlblwiXHJcbiAgICAgID5cclxuICAgICAgICB7LyogU2VjdGlvbiBCYWNrZ3JvdW5kICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoZXhhZ29uLXBhdHRlcm4gb3BhY2l0eS0xMFwiIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNiByZWxhdGl2ZSB6LTEwXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTIwXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWJsb2NrIG1iLTZcIj5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy00MDAgdGV4dC1zbSBmb250LWJvbGQgdHJhY2tpbmctWzAuM2VtXSB1cHBlcmNhc2UgYm9yZGVyIGJvcmRlci15ZWxsb3ctNDAwLzMwIHB4LTYgcHktMiByb3VuZGVkLWZ1bGxcIj5cclxuICAgICAgICAgICAgICAgIOKaoSBMQSBGT1JNVUxBIFBFUkZFVFRBIOKaoVxyXG4gICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTV4bCBtZDp0ZXh0LTd4bCBmb250LWJsYWNrIG1iLTggbGVhZGluZy10aWdodFwiPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtZ3JhZGllbnQtM1wiPkxBIE5PU1RSQTwvc3Bhbj5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXllbGxvdy00MDAgdGV4dC1zaGFkb3ctZ2xvd1wiPkZPUk1VTEE8L3NwYW4+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1ncmFkaWVudC00XCI+TEEgVFVBIEZPUlpBPC9zcGFuPlxyXG4gICAgICAgICAgICA8L2gyPlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS0zMDAgbWF4LXctNHhsIG14LWF1dG8gbGVhZGluZy1yZWxheGVkXCI+XHJcbiAgICAgICAgICAgICAgT2duaSBsYXR0aW5hIGRpIEFmcmlDb2xhIEVuZXJneSDDqCBwZW5zYXRhIHBlciBkYXJ0aSB1bm8gc2xhbmNpbyBuYXR1cmFsZSwgZHVyYXR1cm8gZSBpbnRlbnNvLlxyXG4gICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICB7LyogM0QgRmVhdHVyZSBDYXJkcyAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtOCBtYi0xNlwiPlxyXG4gICAgICAgICAgICB7W1xyXG4gICAgICAgICAgICAgIHsgdGl0bGU6IFwiQUxUQSBET1NFIERJIENBRkZFSU5BXCIsIGljb246IFwi4pqhXCIsIGNvbG9yOiBcImZyb20teWVsbG93LTQwMCB0by1vcmFuZ2UtNTAwXCIgfSxcclxuICAgICAgICAgICAgICB7IHRpdGxlOiBcIkVTVFJBVFRPIE5BVFVSQUxFIERJIE5PQ0UgREkgQ09MQVwiLCBpY29uOiBcIvCfjLBcIiwgY29sb3I6IFwiZnJvbS1hbWJlci00MDAgdG8teWVsbG93LTYwMFwiIH0sXHJcbiAgICAgICAgICAgICAgeyB0aXRsZTogXCJTRU5aQSBDT01QUk9NRVNTSVwiLCBpY29uOiBcIvCfkqpcIiwgY29sb3I6IFwiZnJvbS15ZWxsb3ctNTAwIHRvLXJlZC01MDBcIiB9LFxyXG4gICAgICAgICAgICAgIHsgdGl0bGU6IFwiUFJPRE9UVEEgSU4gVFVOSVNJQVwiLCBpY29uOiBcIvCfh7nwn4ezXCIsIGNvbG9yOiBcImZyb20tcmVkLTUwMCB0by15ZWxsb3ctNDAwXCIgfSxcclxuICAgICAgICAgICAgICB7IHRpdGxlOiBcIkRFU0lHTiBJVEFMSUFOT1wiLCBpY29uOiBcIvCfjqhcIiwgY29sb3I6IFwiZnJvbS1ncmVlbi01MDAgdG8teWVsbG93LTQwMFwiIH0sXHJcbiAgICAgICAgICAgIF0ubWFwKChmZWF0dXJlLCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJncm91cCByZWxhdGl2ZSBmZWF0dXJlLWNhcmQgdHJhbnNmb3JtLWdwdVwiXHJcbiAgICAgICAgICAgICAgICBzdHlsZT17eyBhbmltYXRpb25EZWxheTogYCR7aW5kZXggKiAyMDB9bXNgIH19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tZ3JheS04MDAvNTAgdG8tZ3JheS05MDAvNTAgcm91bmRlZC0zeGwgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLXllbGxvdy00MDAvMjAgZ3JvdXAtaG92ZXI6Ym9yZGVyLXllbGxvdy00MDAvNTAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwXCIgLz5cclxuICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1iciAke2ZlYXR1cmUuY29sb3J9IG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTIwIHJvdW5kZWQtM3hsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMGB9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBwLTggdGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTZ4bCBtYi02IGdyb3VwLWhvdmVyOnNjYWxlLTEyNSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi01MDAgYW5pbWF0ZS1ib3VuY2Utc2xvd1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtmZWF0dXJlLmljb259XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC15ZWxsb3ctNDAwIG1iLTQgbGVhZGluZy10aWdodFwiPntmZWF0dXJlLnRpdGxlfTwvaDM+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEgYmcteWVsbG93LTQwMCBteC1hdXRvIHJvdW5kZWQtZnVsbCBncm91cC1ob3Zlcjp3LTIwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICB7LyogRW5lcmd5IFZpc3VhbGl6YXRpb24gKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCBiZy1ncmFkaWVudC10by1yIGZyb20teWVsbG93LTQwMC8yMCB0by10cmFuc3BhcmVudCBwLTggcm91bmRlZC1mdWxsIGJvcmRlciBib3JkZXIteWVsbG93LTQwMC8zMFwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy00IGgtNCBiZy15ZWxsb3ctNDAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlXCIgLz5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy00MDAgZm9udC1ib2xkIHRyYWNraW5nLXdpZGVyXCI+RU5FUkdJQSBQVVJBPC9zcGFuPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy00IGgtNCBiZy15ZWxsb3ctNDAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlIGRlbGF5LTUwMFwiIC8+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNDAwIGZvbnQtYm9sZCB0cmFja2luZy13aWRlclwiPkdVU1RPIEFVVEVOVElDTzwvc3Bhbj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgYmcteWVsbG93LTQwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1wdWxzZSBkZWxheS0xMDAwXCIgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9zZWN0aW9uPlxyXG5cclxuICAgICAgey8qIElERU5USVRZIFNFQ1RJT04gV0lUSCBNT1JQSElORyBFTEVNRU5UUyAqL31cclxuICAgICAgPHNlY3Rpb24gaWQ9XCJpZGVudGl0YVwiIGNsYXNzTmFtZT1cInB5LTMyIGJnLWdyYWRpZW50LXRvLWIgZnJvbS1ibGFjayB0by1ncmF5LTkwMCByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW5cIj5cclxuICAgICAgICB7LyogQW5pbWF0ZWQgQmFja2dyb3VuZCAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTBcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwid2F2ZS1wYXR0ZXJuXCIgLz5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC02IHJlbGF0aXZlIHotMTBcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMjBcIj5cclxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtNXhsIG1kOnRleHQtN3hsIGZvbnQtYmxhY2sgbWItOFwiPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtZ3JhZGllbnQtNVwiPlVOIFBPTlRFIFRSQTwvc3Bhbj5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXllbGxvdy00MDAgdGV4dC1zaGFkb3ctZ2xvd1wiPkRVRSBDVUxUVVJFPC9zcGFuPlxyXG4gICAgICAgICAgICA8L2gyPlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGxnOmdyaWQtY29scy0yIGdhcC0xNiBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LThcIj5cclxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS0zMDAgbGVhZGluZy1yZWxheGVkXCI+XHJcbiAgICAgICAgICAgICAgICBBZnJpQ29sYSBFbmVyZ3kgbmFzY2UgZGFsbCdpbmNvbnRybyB0cmEgZHVlIG1vbmRpOiBsYSBmb3J6YSBlIGxhIHZpdGFsaXTDoCBkZWxsYSBUdW5pc2lhLCBlIGxhXHJcbiAgICAgICAgICAgICAgICByYWZmaW5hdGV6emEgY3JlYXRpdmEgZGVsbCdJdGFsaWEuXHJcbiAgICAgICAgICAgICAgPC9wPlxyXG5cclxuICAgICAgICAgICAgICB7LyogQ291bnRyeSBDYXJkcyAqL31cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncm91cCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTYgcC02IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1yZWQtNjAwLzIwIHRvLXRyYW5zcGFyZW50IHJvdW5kZWQtMnhsIGJvcmRlciBib3JkZXItcmVkLTUwMC8zMCBob3Zlcjpib3JkZXItcmVkLTUwMC82MCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDBcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYmctcmVkLTYwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC0zeGwgZ3JvdXAtaG92ZXI6c2NhbGUtMTEwIHRyYW5zaXRpb24tdHJhbnNmb3JtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAg8J+HufCfh7NcclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXJlZC00MDBcIj5UVU5JU0lBPC9oMz5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+Rm9yemEgZSBWaXRhbGl0w6AgZGVsIE1lZGl0ZXJyYW5lbzwvcD5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyb3VwIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNiBwLTYgYmctZ3JhZGllbnQtdG8tciBmcm9tLWdyZWVuLTYwMC8yMCB0by10cmFuc3BhcmVudCByb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLWdyZWVuLTUwMC8zMCBob3Zlcjpib3JkZXItZ3JlZW4tNTAwLzYwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1ncmVlbi02MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtM3hsIGdyb3VwLWhvdmVyOnNjYWxlLTExMCB0cmFuc2l0aW9uLXRyYW5zZm9ybVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIPCfh67wn4e5XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmVlbi00MDBcIj5JVEFMSUE8L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDBcIj5SYWZmaW5hdGV6emEgZSBEZXNpZ24gRWxlZ2FudGU8L3A+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgey8qIE1lZGl0ZXJyYW5lYW4gVmlzdWFsaXphdGlvbiAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWVkaXRlcnJhbmVhbi1tYXBcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNjAwLzMwIHRvLWN5YW4tNDAwLzMwIHJvdW5kZWQtM3hsIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci1jeWFuLTQwMC8zMFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTAgcC0xMiB0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtNnhsIG1iLTYgYW5pbWF0ZS1ib3VuY2Utc2xvd1wiPvCfjIo8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWN5YW4tNDAwIG1iLTRcIj5NRURJVEVSUkFORU88L2gzPlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIHRleHQtbGcgbGVhZGluZy1yZWxheGVkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgSWwgbm9zdHJvIG1hcmUsIGxhIG5vc3RyYSBpc3BpcmF6aW9uZSwgbGEgbm9zdHJhIGVuZXJnaWEgY2hlIHVuaXNjZSBkdWUgY3VsdHVyZSBpbiB1bmEgc29sYVxyXG4gICAgICAgICAgICAgICAgICAgIGVzcGVyaWVuemEuXHJcbiAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC04IGZsZXgganVzdGlmeS1jZW50ZXIgc3BhY2UteC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMgaC0zIGJnLWN5YW4tNDAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMyBoLTMgYmctY3lhbi00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2UgZGVsYXktMzAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMyBoLTMgYmctY3lhbi00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2UgZGVsYXktNjAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9zZWN0aW9uPlxyXG5cclxuICAgICAgey8qIFBVUkNIQVNFIFNFQ1RJT04gV0lUSCBIT0xPR1JBUEhJQyBDQVJEUyAqL31cclxuICAgICAgPHNlY3Rpb24gaWQ9XCJhY3F1aXN0YVwiIGNsYXNzTmFtZT1cInB5LTMyIGJnLWdyYWRpZW50LXRvLWIgZnJvbS1ncmF5LTkwMCB0by1ibGFjayByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW5cIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTYgcmVsYXRpdmUgei0xMFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0yMFwiPlxyXG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC01eGwgbWQ6dGV4dC03eGwgZm9udC1ibGFjayBtYi04XCI+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1ncmFkaWVudC02XCI+U0NFR0xJIExBIFRVQTwvc3Bhbj5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXllbGxvdy00MDAgdGV4dC1zaGFkb3ctZ2xvd1wiPkRPU0UgREkgRU5FUkdJQTwvc3Bhbj5cclxuICAgICAgICAgICAgPC9oMj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBtZDpncmlkLWNvbHMtMyBnYXAtOCBtYi0xNlwiPlxyXG4gICAgICAgICAgICB7W1xyXG4gICAgICAgICAgICAgIHsgcXVhbnRpdHk6IFwiMSBMQVRUSU5BXCIsIHByaWNlOiBcIjIuNTBcIiwgc3VidGl0bGU6IFwiUHJvdmEgU2luZ29sYVwiLCBwb3B1bGFyOiBmYWxzZSB9LFxyXG4gICAgICAgICAgICAgIHsgcXVhbnRpdHk6IFwiNiBMQVRUSU5FXCIsIHByaWNlOiBcIjE0LjAwXCIsIHN1YnRpdGxlOiBcIlBhY2sgRmFtaWdsaWFcIiwgcG9wdWxhcjogdHJ1ZSB9LFxyXG4gICAgICAgICAgICAgIHsgcXVhbnRpdHk6IFwiMTIgTEFUVElORVwiLCBwcmljZTogXCIyNi4wMFwiLCBzdWJ0aXRsZTogXCJTY29ydGEgRW5lcmdpYVwiLCBwb3B1bGFyOiBmYWxzZSB9LFxyXG4gICAgICAgICAgICBdLm1hcCgocGFjaywgaW5kZXgpID0+IChcclxuICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZ3JvdXAgcmVsYXRpdmUgcHJpY2luZy1jYXJkIHRyYW5zZm9ybS1ncHUgJHtwYWNrLnBvcHVsYXIgPyBcInBvcHVsYXItY2FyZFwiIDogXCJcIn1gfVxyXG4gICAgICAgICAgICAgICAgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6IGAke2luZGV4ICogMjAwfW1zYCB9fVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtwYWNrLnBvcHVsYXIgJiYgKFxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtNiBsZWZ0LTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS14LTEvMiB6LTIwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20teWVsbG93LTQwMCB0by15ZWxsb3ctNjAwIHRleHQtYmxhY2sgcHgtNiBweS0zIHJvdW5kZWQtZnVsbCB0ZXh0LXNtIGZvbnQtYmxhY2sgdHJhY2tpbmctd2lkZXIgYW5pbWF0ZS1wdWxzZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAg4pqhIFBJw5kgUE9QT0xBUkUg4pqhXHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTgwMC81MCB0by1ncmF5LTkwMC81MCByb3VuZGVkLTN4bCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXIteWVsbG93LTQwMC8yMCBncm91cC1ob3Zlcjpib3JkZXIteWVsbG93LTQwMC82MCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDBcIiAvPlxyXG4gICAgICAgICAgICAgICAge3BhY2sucG9wdWxhciAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1iciBmcm9tLXllbGxvdy00MDAvMjAgdG8tb3JhbmdlLTUwMC8yMCByb3VuZGVkLTN4bFwiIC8+XHJcbiAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBwLTEwIHRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC15ZWxsb3ctNDAwIG1iLTJcIj57cGFjay5xdWFudGl0eX08L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPntwYWNrLnN1YnRpdGxlfTwvcD5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtNXhsIGZvbnQtYmxhY2sgbWItMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZVwiPntwYWNrLnByaWNlfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteWVsbG93LTQwMCB0ZXh0LTJ4bCBtbC0yXCI+VE5EPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYWRpZW50LXRvLXIgZnJvbS15ZWxsb3ctNDAwIHRvLXllbGxvdy02MDAgdGV4dC1ibGFjayBweS00IHJvdW5kZWQtMnhsIGZvbnQtYm9sZCB0ZXh0LWxnIGhvdmVyOnNoYWRvdy0yeGwgaG92ZXI6c2hhZG93LXllbGxvdy00MDAvNTAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIHRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMDUgZ3JvdXAtaG92ZXI6LXJvdGF0ZS0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgT1JESU5BIE9SQSDimqFcclxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHNwYWNlLXktOFwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgYmctZ3JhZGllbnQtdG8tciBmcm9tLXllbGxvdy00MDAvMjAgdG8tdHJhbnNwYXJlbnQgcC02IHJvdW5kZWQtZnVsbCBib3JkZXIgYm9yZGVyLXllbGxvdy00MDAvMzBcIj5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy00MDAgZm9udC1ib2xkIHRleHQteGxcIj7wn5qaIFNwZWRpemlvbmUgaW4gdHV0dGEgbGEgVHVuaXNpYTwvc3Bhbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cImdyb3VwIGJnLXRyYW5zcGFyZW50IGJvcmRlci0yIGJvcmRlci15ZWxsb3ctNDAwIHRleHQteWVsbG93LTQwMCBweC0xMCBweS00IHJvdW5kZWQtZnVsbCBmb250LWJvbGQgdGV4dC1sZyBob3ZlcjpiZy15ZWxsb3ctNDAwIGhvdmVyOnRleHQtYmxhY2sgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIHRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMDVcIj5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAg8J+TjSBUUk9WQSBJTCBQVU5UTyBWRU5ESVRBIFBJw5kgVklDSU5PXHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0zIGdyb3VwLWhvdmVyOnRyYW5zbGF0ZS14LTIgdHJhbnNpdGlvbi10cmFuc2Zvcm1cIj7ihpI8L3NwYW4+XHJcbiAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L3NlY3Rpb24+XHJcblxyXG4gICAgICB7LyogQ09OVEFDVCBTRUNUSU9OICovfVxyXG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0zMiBiZy1ncmFkaWVudC10by10IGZyb20tYmxhY2sgdG8tZ3JheS05MDAgcmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC02IHJlbGF0aXZlIHotMTBcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMjBcIj5cclxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtNXhsIG1kOnRleHQtN3hsIGZvbnQtYmxhY2sgbWItOFwiPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtZ3JhZGllbnQtN1wiPkNPTlRBVFRBQ0kgTzwvc3Bhbj5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXllbGxvdy00MDAgdGV4dC1zaGFkb3ctZ2xvd1wiPkRJVkVOVEEgUEFSVE5FUjwvc3Bhbj5cclxuICAgICAgICAgICAgPC9oMj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBsZzpncmlkLWNvbHMtMiBnYXAtMTZcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LThcIj5cclxuICAgICAgICAgICAgICB7W1xyXG4gICAgICAgICAgICAgICAgeyBpY29uOiBcIvCfk6dcIiwgdGl0bGU6IFwiRU1BSUxcIiwgaW5mbzogXCJpbmZvQGFmcmljb2xhLWVuZXJneS50blwiIH0sXHJcbiAgICAgICAgICAgICAgICB7IGljb246IFwi8J+TsVwiLCB0aXRsZTogXCJURUxFRk9OT1wiLCBpbmZvOiBcIisyMTYgWFggWFhYIFhYWFwiIH0sXHJcbiAgICAgICAgICAgICAgICB7IGljb246IFwi8J+TjVwiLCB0aXRsZTogXCJTRURFXCIsIGluZm86IFwiVHVuaXMsIFR1bmlzaWFcIiB9LFxyXG4gICAgICAgICAgICAgIF0ubWFwKChjb250YWN0LCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJncm91cCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTYgcC02IGJnLWdyYWRpZW50LXRvLXIgZnJvbS15ZWxsb3ctNDAwLzEwIHRvLXRyYW5zcGFyZW50IHJvdW5kZWQtMnhsIGJvcmRlciBib3JkZXIteWVsbG93LTQwMC8zMCBob3Zlcjpib3JkZXIteWVsbG93LTQwMC82MCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDBcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy15ZWxsb3ctNDAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LTJ4bCB0ZXh0LWJsYWNrIGdyb3VwLWhvdmVyOnNjYWxlLTExMCB0cmFuc2l0aW9uLXRyYW5zZm9ybVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtjb250YWN0Lmljb259XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LXllbGxvdy00MDBcIj57Y29udGFjdC50aXRsZX08L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgdGV4dC1sZ1wiPntjb250YWN0LmluZm99PC9wPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTgwMC81MCB0by1ncmF5LTkwMC81MCBwLTEwIHJvdW5kZWQtM3hsIGJvcmRlciBib3JkZXIteWVsbG93LTQwMC8zMCBiYWNrZHJvcC1ibHVyLXNtXCI+XHJcbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXllbGxvdy00MDAgbWItOCB0ZXh0LWNlbnRlclwiPkRJVkVOVEEgUEFSVE5FUjwvaDM+XHJcbiAgICAgICAgICAgICAgPGZvcm0gY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XHJcbiAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIk5vbWUgQXR0aXZpdMOgXCJcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktNzAwLzUwIGJvcmRlciBib3JkZXItZ3JheS02MDAgcm91bmRlZC0yeGwgcHgtNiBweS00IHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDAgZm9jdXM6Ym9yZGVyLXllbGxvdy00MDAgZm9jdXM6b3V0bGluZS1ub25lIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXHJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW1haWxcIlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS03MDAvNTAgYm9yZGVyIGJvcmRlci1ncmF5LTYwMCByb3VuZGVkLTJ4bCBweC02IHB5LTQgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci1ncmF5LTQwMCBmb2N1czpib3JkZXIteWVsbG93LTQwMCBmb2N1czpvdXRsaW5lLW5vbmUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8dGV4dGFyZWFcclxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJNZXNzYWdnaW9cIlxyXG4gICAgICAgICAgICAgICAgICByb3dzPXs0fVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS03MDAvNTAgYm9yZGVyIGJvcmRlci1ncmF5LTYwMCByb3VuZGVkLTJ4bCBweC02IHB5LTQgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci1ncmF5LTQwMCBmb2N1czpib3JkZXIteWVsbG93LTQwMCBmb2N1czpvdXRsaW5lLW5vbmUgcmVzaXplLW5vbmUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcclxuICAgICAgICAgICAgICAgID48L3RleHRhcmVhPlxyXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYWRpZW50LXRvLXIgZnJvbS15ZWxsb3ctNDAwIHRvLXllbGxvdy02MDAgdGV4dC1ibGFjayBweS00IHJvdW5kZWQtMnhsIGZvbnQtYm9sZCB0ZXh0LWxnIGhvdmVyOnNoYWRvdy0yeGwgaG92ZXI6c2hhZG93LXllbGxvdy00MDAvNTAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIHRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMDVcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICBJTlZJQSBSSUNISUVTVEEg4pqhXHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICA8L2Zvcm0+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvc2VjdGlvbj5cclxuXHJcbiAgICAgIHsvKiBGVVRVUklTVElDIEZPT1RFUiAqL31cclxuICAgICAgPGZvb3RlciBjbGFzc05hbWU9XCJiZy1ibGFjayBib3JkZXItdCBib3JkZXIteWVsbG93LTQwMC8zMCBweS0xMiByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW5cIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTBcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9vdGVyLXBhdHRlcm4gb3BhY2l0eS0xMFwiIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNiB0ZXh0LWNlbnRlciByZWxhdGl2ZSB6LTEwXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJsYWNrIHRleHQteWVsbG93LTQwMCBtYi00IGdsaXRjaC10ZXh0XCI+yLpG4rGkxpfIu8OYxYHIuiBFTkVSR1k8L2Rpdj5cclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXhsIG1iLTZcIj5MJ2VuZXJnaWEgY2hlIG5hc2NlIGRhbCBNZWRpdGVycmFuZW88L3A+XHJcblxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgc3BhY2UteC04IHRleHQtZ3JheS00MDAgdGV4dC1zbVwiPlxyXG4gICAgICAgICAgICAgIDxzcGFuPsKpIDIwMjQgQWZyaUNvbGEgRW5lcmd5PC9zcGFuPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteWVsbG93LTQwMFwiPuKAojwvc3Bhbj5cclxuICAgICAgICAgICAgICA8c3Bhbj5NYWRlIGluIFR1bmlzaWEg8J+HufCfh7M8L3NwYW4+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNDAwXCI+4oCiPC9zcGFuPlxyXG4gICAgICAgICAgICAgIDxzcGFuPkRlc2lnbmVkIHdpdGggSXRhbGlhbiBQYXNzaW9uIPCfh67wn4e5PC9zcGFuPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLXllbGxvdy00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2VcIiAvPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmcteWVsbG93LTQwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1wdWxzZSBkZWxheS0zMDBcIiAvPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmcteWVsbG93LTQwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1wdWxzZSBkZWxheS02MDBcIiAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZm9vdGVyPlxyXG4gICAgPC9kaXY+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJ1c2VTdGF0ZSIsIkFmcmlDb2xhV2Vic2l0ZSIsImhlcm9SZWYiLCJjYW5SZWYiLCJtb3VzZVBvc2l0aW9uIiwic2V0TW91c2VQb3NpdGlvbiIsIngiLCJ5Iiwic2Nyb2xsWSIsInNldFNjcm9sbFkiLCJoYW5kbGVNb3VzZU1vdmUiLCJlIiwiY2xpZW50WCIsImNsaWVudFkiLCJoYW5kbGVTY3JvbGwiLCJ3aW5kb3ciLCJjdXJyZW50Iiwic2Nyb2xsZWQiLCJwYWdlWU9mZnNldCIsInJhdGUiLCJyb3RhdGUiLCJzdHlsZSIsInRyYW5zZm9ybSIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwic2Nyb2xsVG9TZWN0aW9uIiwic2VjdGlvbklkIiwiZG9jdW1lbnQiLCJnZXRFbGVtZW50QnlJZCIsInNjcm9sbEludG9WaWV3IiwiYmVoYXZpb3IiLCJkaXYiLCJjbGFzc05hbWUiLCJBcnJheSIsIm1hcCIsIl8iLCJpIiwibGVmdCIsIk1hdGgiLCJyYW5kb20iLCJ0b3AiLCJhbmltYXRpb25EZWxheSIsImFuaW1hdGlvbkR1cmF0aW9uIiwic2luIiwiRGF0ZSIsIm5vdyIsIm5hdiIsIml0ZW0iLCJidXR0b24iLCJvbkNsaWNrIiwidG9Mb3dlckNhc2UiLCJyZXBsYWNlIiwic3BhbiIsInNlY3Rpb24iLCJpZCIsInJlZiIsImgxIiwicCIsImltZyIsInNyYyIsImFsdCIsImZpbHRlciIsImgyIiwidGl0bGUiLCJpY29uIiwiY29sb3IiLCJmZWF0dXJlIiwiaW5kZXgiLCJoMyIsInF1YW50aXR5IiwicHJpY2UiLCJzdWJ0aXRsZSIsInBvcHVsYXIiLCJwYWNrIiwiaW5mbyIsImNvbnRhY3QiLCJmb3JtIiwiaW5wdXQiLCJ0eXBlIiwicGxhY2Vob2xkZXIiLCJ0ZXh0YXJlYSIsInJvd3MiLCJmb290ZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});