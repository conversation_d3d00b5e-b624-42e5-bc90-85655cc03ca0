"use client"

import { useEffect, useRef } from "react"

export default function AfriColaWebsite() {
  const heroRef = useRef<HTMLElement>(null)
  const canRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.pageYOffset
      const parallax = scrolled * 0.5

      if (canRef.current) {
        canRef.current.style.transform = `translateY(${parallax}px) rotate(${scrolled * 0.1}deg)`
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: "smooth" })
  }

  return (
    <div className="min-h-screen bg-black text-white overflow-x-hidden">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-black/80 backdrop-blur-md border-b border-yellow-400/20">
        <div className="max-w-7xl mx-auto px-6 py-4 flex justify-between items-center">
          <div className="text-2xl font-bold text-yellow-400">AFRICOLA</div>
          <div className="hidden md:flex space-x-8">
            <button onClick={() => scrollToSection("hero")} className="hover:text-yellow-400 transition-colors">
              HOME
            </button>
            <button onClick={() => scrollToSection("product")} className="hover:text-yellow-400 transition-colors">
              PRODOTTO
            </button>
            <button onClick={() => scrollToSection("identity")} className="hover:text-yellow-400 transition-colors">
              IDENTITÀ
            </button>
            <button onClick={() => scrollToSection("purchase")} className="hover:text-yellow-400 transition-colors">
              ACQUISTA
            </button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section
        id="hero"
        ref={heroRef}
        className="relative min-h-screen flex items-center justify-center overflow-hidden"
      >
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>

        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-yellow-400/5 rounded-full blur-2xl animate-pulse delay-1000"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-6 grid lg:grid-cols-2 gap-12 items-center">
          <div className="text-center lg:text-left">
            <h1 className="text-5xl md:text-7xl font-black mb-6 leading-tight">
              L'ENERGIA CHE NASCE DAL <span className="text-yellow-400 animate-pulse">MEDITERRANEO</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-300 leading-relaxed">
              AfriCola Energy non è una semplice bevanda: è un'esperienza. Creata in Tunisia e ispirata dal design e
              dalla passione italiana, questa lattina gialla sprigiona una carica intensa grazie all'estratto naturale
              di noce di cola.
            </p>
            <div className="space-y-4">
              <button className="group bg-yellow-400 text-black px-8 py-4 text-xl font-bold rounded-full hover:bg-yellow-300 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-yellow-400/50">
                PROVALA ORA
                <span className="ml-2 group-hover:translate-x-2 transition-transform inline-block">→</span>
              </button>
              <p className="text-yellow-400 font-semibold text-lg">Sentila tua. Vivi AfriCola Energy.</p>
            </div>
          </div>

          <div className="relative flex justify-center">
            <div ref={canRef} className="relative">
              <div className="absolute inset-0 bg-yellow-400/20 rounded-full blur-3xl animate-pulse"></div>
              <img
                src="/africola-can.png"
                alt="AfriCola Energy Can"
                className="relative z-10 w-80 h-auto drop-shadow-2xl hover:scale-110 transition-transform duration-500"
              />
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-yellow-400 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-yellow-400 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Product Section */}
      <section id="product" className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-6xl font-black mb-6">
              LA NOSTRA <span className="text-yellow-400">FORMULA</span>.<br />
              LA TUA <span className="text-yellow-400">FORZA</span>.
            </h2>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Ogni lattina di AfriCola Energy è pensata per darti uno slancio naturale, duraturo e intenso. Con un'alta
              concentrazione di caffeina e il gusto autentico della noce di cola, è la scelta ideale per chi vive senza
              compromessi.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              { title: "ALTA DOSE DI CAFFEINA", icon: "⚡" },
              { title: "ESTRATTO NATURALE DI NOCE DI COLA", icon: "🌰" },
              { title: "SENZA COMPROMESSI: SOLO ENERGIA REALE", icon: "💪" },
              { title: "PRODOTTA IN TUNISIA", icon: "🇹🇳" },
              { title: "DESIGN ISPIRATO ALL'ELEGANZA ITALIANA", icon: "🎨" },
            ].map((feature, index) => (
              <div
                key={index}
                className="group bg-gray-800/50 p-8 rounded-2xl border border-yellow-400/20 hover:border-yellow-400/50 transition-all duration-300 hover:transform hover:scale-105"
              >
                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform">{feature.icon}</div>
                <h3 className="text-xl font-bold text-yellow-400 mb-2">{feature.title}</h3>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Identity Section */}
      <section id="identity" className="py-20 bg-gradient-to-b from-gray-900 to-black relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-yellow-400 to-transparent"></div>
        </div>

        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-6xl font-black mb-6">
              UN PONTE TRA <span className="text-yellow-400">DUE CULTURE</span>
            </h2>
            <div className="max-w-4xl mx-auto">
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                AfriCola Energy nasce dall'incontro tra due mondi: la forza e la vitalità della Tunisia, e la
                raffinatezza creativa dell'Italia. È una celebrazione del Mar Mediterraneo — un simbolo di unione,
                passione e movimento.
              </p>
              <p className="text-lg text-gray-400 leading-relaxed">
                Dietro ogni lattina c'è una visione: offrire energia autentica, rispettando le radici culturali e
                valorizzando il design e la qualità.
              </p>
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center text-2xl">🇹🇳</div>
                <div>
                  <h3 className="text-2xl font-bold text-yellow-400">TUNISIA</h3>
                  <p className="text-gray-300">Forza e Vitalità</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-2xl">🇮🇹</div>
                <div>
                  <h3 className="text-2xl font-bold text-yellow-400">ITALIA</h3>
                  <p className="text-gray-300">Raffinatezza e Design</p>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-red-600/20 to-green-600/20 rounded-3xl blur-xl"></div>
              <div className="relative bg-gray-800/50 p-8 rounded-3xl border border-yellow-400/30">
                <h3 className="text-3xl font-bold text-yellow-400 mb-4">MEDITERRANEO</h3>
                <p className="text-gray-300 text-lg">Il nostro mare, la nostra ispirazione, la nostra energia.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Purchase Section */}
      <section id="purchase" className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-6xl font-black mb-6">
              SCEGLI LA TUA DOSE DI <span className="text-yellow-400">ENERGIA</span>
            </h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-12">
            {[
              { quantity: "1 LATTINA", price: "2.50 TND", popular: false },
              { quantity: "6 LATTINE", price: "14.00 TND", popular: true },
              { quantity: "12 LATTINE", price: "26.00 TND", popular: false },
            ].map((pack, index) => (
              <div
                key={index}
                className={`relative bg-gray-800/50 p-8 rounded-2xl border transition-all duration-300 hover:transform hover:scale-105 ${pack.popular ? "border-yellow-400 bg-yellow-400/10" : "border-gray-700 hover:border-yellow-400/50"}`}
              >
                {pack.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-yellow-400 text-black px-4 py-2 rounded-full text-sm font-bold">
                    PIÙ POPOLARE
                  </div>
                )}
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-yellow-400 mb-4">{pack.quantity}</h3>
                  <div className="text-4xl font-black mb-6">{pack.price}</div>
                  <button className="w-full bg-yellow-400 text-black py-3 rounded-full font-bold hover:bg-yellow-300 transition-colors">
                    ORDINA ORA
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center">
            <p className="text-xl text-gray-300 mb-6">Spedizione in tutta la Tunisia</p>
            <button className="bg-transparent border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold hover:bg-yellow-400 hover:text-black transition-all duration-300">
              TROVA IL PUNTO VENDITA PIÙ VICINO
            </button>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-gradient-to-t from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-6xl font-black mb-6">
              CONTATTACI O DIVENTA <span className="text-yellow-400">PARTNER</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Hai un negozio, un bar, una palestra o un'attività e vuoi distribuire AfriCola Energy? Contattaci per
              scoprire le opportunità di collaborazione. Siamo sempre alla ricerca di nuovi partner pronti a condividere
              la nostra visione.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12">
            <div className="space-y-8">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-yellow-400 rounded-full flex items-center justify-center text-black text-xl">
                  📧
                </div>
                <div>
                  <h3 className="text-xl font-bold text-yellow-400">EMAIL</h3>
                  <p className="text-gray-300"><EMAIL></p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-yellow-400 rounded-full flex items-center justify-center text-black text-xl">
                  📱
                </div>
                <div>
                  <h3 className="text-xl font-bold text-yellow-400">TELEFONO</h3>
                  <p className="text-gray-300">+216 XX XXX XXX</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-yellow-400 rounded-full flex items-center justify-center text-black text-xl">
                  📍
                </div>
                <div>
                  <h3 className="text-xl font-bold text-yellow-400">SEDE</h3>
                  <p className="text-gray-300">Tunis, Tunisia</p>
                </div>
              </div>
            </div>
            <div className="bg-gray-800/50 p-8 rounded-2xl border border-yellow-400/30">
              <h3 className="text-2xl font-bold text-yellow-400 mb-6">DIVENTA PARTNER</h3>
              <form className="space-y-4">
                <input
                  type="text"
                  placeholder="Nome Attività"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-yellow-400 focus:outline-none"
                />
                <input
                  type="email"
                  placeholder="Email"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-yellow-400 focus:outline-none"
                />
                <textarea
                  placeholder="Messaggio"
                  rows={4}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-yellow-400 focus:outline-none resize-none"
                ></textarea>
                <button
                  type="submit"
                  className="w-full bg-yellow-400 text-black py-3 rounded-lg font-bold hover:bg-yellow-300 transition-colors"
                >
                  INVIA RICHIESTA
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black border-t border-yellow-400/20 py-8">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <div className="text-3xl font-bold text-yellow-400 mb-4">AFRICOLA ENERGY</div>
          <p className="text-gray-400 mb-4">L'energia che nasce dal Mediterraneo</p>
          <div className="flex justify-center space-x-6 text-gray-400">
            <span>© 2024 AfriCola Energy</span>
            <span>•</span>
            <span>Made in Tunisia</span>
            <span>•</span>
            <span>Designed with Italian Passion</span>
          </div>
        </div>
      </footer>
    </div>
  )
}
