{"version": 3, "sources": ["../../../src/lib/eslint/writeDefaultConfig.ts"], "names": ["promises", "fs", "bold", "green", "os", "path", "CommentJson", "Log", "writeDefaultConfig", "baseDir", "exists", "emptyEslintrc", "emptyPkgJsonConfig", "selectedConfig", "eslintrcFile", "pkgJsonPath", "packageJsonConfig", "ext", "extname", "newFileContent", "stringify", "writeFile", "EOL", "info", "basename", "eslintConfig", "join", "console", "log"], "mappings": "AAAA,SAASA,YAAYC,EAAE,QAAQ,KAAI;AACnC,SAASC,IAAI,EAAEC,KAAK,QAAQ,gBAAe;AAC3C,OAAOC,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,YAAYC,iBAAiB,kCAAiC;AAG9D,YAAYC,SAAS,yBAAwB;AAE7C,OAAO,eAAeC,mBACpBC,OAAe,EACf,EAAEC,MAAM,EAAEC,aAAa,EAAEC,kBAAkB,EAAmB,EAC9DC,cAAmB,EACnBC,YAA2B,EAC3BC,WAA0B,EAC1BC,iBAA+C;IAE/C,IAAI,CAACN,UAAUC,iBAAiBG,cAAc;QAC5C,MAAMG,MAAMZ,KAAKa,OAAO,CAACJ;QAEzB,IAAIK;QACJ,IAAIF,QAAQ,WAAWA,QAAQ,QAAQ;YACrCE,iBAAiB;QACnB,OAAO;YACLA,iBAAiBb,YAAYc,SAAS,CAACP,gBAAgB,MAAM;YAE7D,IAAII,QAAQ,OAAO;gBACjBE,iBAAiB,sBAAsBA;YACzC;QACF;QAEA,MAAMlB,GAAGoB,SAAS,CAACP,cAAcK,iBAAiBf,GAAGkB,GAAG;QAExDf,IAAIgB,IAAI,CACN,CAAC,gDAAgD,EAAErB,KACjDG,KAAKmB,QAAQ,CAACV,eACd,yBAAyB,CAAC;IAEhC,OAAO,IAAI,CAACJ,UAAUE,sBAAsBI,mBAAmB;QAC7DA,kBAAkBS,YAAY,GAAGZ;QAEjC,IAAIE,aACF,MAAMd,GAAGoB,SAAS,CAChBN,aACAT,YAAYc,SAAS,CAACJ,mBAAmB,MAAM,KAAKZ,GAAGkB,GAAG;QAG9Df,IAAIgB,IAAI,CACN,CAAC,qBAAqB,EAAErB,KACtB,gBACA,8CAA8C,CAAC;IAErD,OAAO,IAAI,CAACQ,QAAQ;QAClB,MAAMT,GAAGoB,SAAS,CAChBhB,KAAKqB,IAAI,CAACjB,SAAS,mBACnBH,YAAYc,SAAS,CAACP,gBAAgB,MAAM,KAAKT,GAAGkB,GAAG;QAGzDK,QAAQC,GAAG,CACTzB,MACE,CAAC,eAAe,EAAED,KAChB,kBACA,uDAAuD,CAAC;IAGhE;AACF"}