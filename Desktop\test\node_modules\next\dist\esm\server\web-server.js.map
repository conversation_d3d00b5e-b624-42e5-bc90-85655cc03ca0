{"version": 3, "sources": ["../../src/server/web-server.ts"], "names": ["byteLength", "BaseServer", "NoFallbackError", "generateETag", "addRequestMeta", "WebResponseCache", "isAPIRoute", "removeTrailingSlash", "isDynamicRoute", "interpolateDynamicPath", "normalizeVercelUrl", "getNamedRouteRegex", "IncrementalCache", "NextWebServer", "constructor", "options", "handleCatchallRenderRequest", "req", "res", "parsedUrl", "pathname", "query", "Error", "normalizedPage", "serverOptions", "webServerConfig", "routeRegex", "Object", "keys", "routeKeys", "i18nProvider", "detectedLocale", "analyze", "__next<PERSON><PERSON><PERSON>", "bubbleNoFallback", "_nextBubbleNoFallback", "render", "err", "assign", "renderOpts", "extendRenderOpts", "getIncrementalCache", "requestHeaders", "dev", "requestProtocol", "appDir", "hasAppDir", "allowedRevalidateHeaderKeys", "nextConfig", "experimental", "minimalMode", "fetchCache", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "isrMemoryCacheSize", "flushToDisk", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "getPrerenderManifest", "getResponseCache", "hasPage", "page", "getBuildId", "buildId", "getHasAppDir", "pagesType", "getPagesManifest", "getAppPathsManifest", "attachRequestMeta", "prerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "previewModeId", "getNextFontManifest", "nextFontManifest", "renderHTML", "renderToHTML", "disableOptimizedLoading", "runtime", "sendRenderResult", "_req", "<PERSON><PERSON><PERSON><PERSON>", "poweredByHeader", "type", "<PERSON><PERSON><PERSON><PERSON>", "result", "contentType", "promise", "isDynamic", "pipeTo", "transformStream", "writable", "payload", "toUnchunkedString", "String", "generateEtags", "body", "send", "findPageComponents", "params", "loadComponent", "components", "run<PERSON><PERSON>", "handleApiRequest", "loadEnvConfig", "getPublicDir", "getHasStaticDir", "get<PERSON>allback", "getFontManifest", "undefined", "handleCompression", "handleUpgrade", "getFallbackErrorComponents", "getRoutesManifest", "getMiddleware", "getFilesystemPaths", "Set", "getPrefetchRsc"], "mappings": "AAeA,SAASA,UAAU,QAAQ,kBAAiB;AAC5C,OAAOC,cAAcC,eAAe,QAAQ,gBAAe;AAC3D,SAASC,YAAY,QAAQ,aAAY;AACzC,SAASC,cAAc,QAAQ,iBAAgB;AAC/C,OAAOC,sBAAsB,uBAAsB;AACnD,SAASC,UAAU,QAAQ,sBAAqB;AAChD,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,iBAAgB;AAC3E,SAASC,kBAAkB,QAAQ,yCAAwC;AAC3E,SAASC,gBAAgB,QAAQ,0BAAyB;AAkB1D,eAAe,MAAMC,sBAAsBZ;IACzCa,YAAYC,OAAyB,CAAE;QACrC,KAAK,CAACA;aA0FEC,8BAA4C,OACpDC,KACAC,KACAC;YAEA,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAE,GAAGF;YAC1B,IAAI,CAACC,UAAU;gBACb,MAAM,IAAIE,MAAM;YAClB;YAEA,4DAA4D;YAC5D,+CAA+C;YAC/C,MAAMC,iBAAiB,IAAI,CAACC,aAAa,CAACC,eAAe,CAACL,QAAQ;YAElE,IAAIA,aAAaG,gBAAgB;gBAC/BH,WAAWG;gBAEX,IAAIf,eAAeY,WAAW;oBAC5B,MAAMM,aAAaf,mBAAmBS,UAAU;oBAChDA,WAAWX,uBAAuBW,UAAUC,OAAOK;oBACnDhB,mBACEO,KACA,MACAU,OAAOC,IAAI,CAACF,WAAWG,SAAS,GAChC,MACAH;gBAEJ;YACF;YAEA,wDAAwD;YACxDN,WAAWb,oBAAoBa;YAE/B,IAAI,IAAI,CAACU,YAAY,EAAE;gBACrB,MAAM,EAAEC,cAAc,EAAE,GAAG,MAAM,IAAI,CAACD,YAAY,CAACE,OAAO,CAACZ;gBAC3D,IAAIW,gBAAgB;oBAClBZ,UAAUE,KAAK,CAACY,YAAY,GAAGF;gBACjC;YACF;YAEA,MAAMG,mBAAmB,CAAC,CAACb,MAAMc,qBAAqB;YAEtD,IAAI7B,WAAWc,WAAW;gBACxB,OAAOC,MAAMc,qBAAqB;YACpC;YAEA,IAAI;gBACF,MAAM,IAAI,CAACC,MAAM,CAACnB,KAAKC,KAAKE,UAAUC,OAAOF,WAAW;gBAExD,OAAO;YACT,EAAE,OAAOkB,KAAK;gBACZ,IAAIA,eAAenC,mBAAmBgC,kBAAkB;oBACtD,OAAO;gBACT;gBACA,MAAMG;YACR;QACF;QAhJE,uBAAuB;QACvBV,OAAOW,MAAM,CAAC,IAAI,CAACC,UAAU,EAAExB,QAAQU,eAAe,CAACe,gBAAgB;IACzE;IAEUC,oBAAoB,EAC5BC,cAAc,EAGf,EAAE;QACD,MAAMC,MAAM,CAAC,CAAC,IAAI,CAACJ,UAAU,CAACI,GAAG;QACjC,wCAAwC;QACxC,kDAAkD;QAClD,oBAAoB;QACpB,OAAO,IAAI/B,iBAAiB;YAC1B+B;YACAD;YACAE,iBAAiB;YACjBC,QAAQ,IAAI,CAACC,SAAS;YACtBC,6BACE,IAAI,CAACC,UAAU,CAACC,YAAY,CAACF,2BAA2B;YAC1DG,aAAa,IAAI,CAACA,WAAW;YAC7BC,YAAY;YACZC,qBAAqB,IAAI,CAACJ,UAAU,CAACC,YAAY,CAACG,mBAAmB;YACrEC,oBAAoB,IAAI,CAACL,UAAU,CAACC,YAAY,CAACK,kBAAkB;YACnEC,aAAa;YACbC,iBACE,IAAI,CAAChC,aAAa,CAACC,eAAe,CAACgC,uBAAuB;YAC5DC,sBAAsB,IAAM,IAAI,CAACA,oBAAoB;QACvD;IACF;IACUC,mBAAmB;QAC3B,OAAO,IAAItD,iBAAiB,IAAI,CAAC6C,WAAW;IAC9C;IAEA,MAAgBU,QAAQC,IAAY,EAAE;QACpC,OAAOA,SAAS,IAAI,CAACrC,aAAa,CAACC,eAAe,CAACoC,IAAI;IACzD;IAEUC,aAAa;QACrB,OAAO,IAAI,CAACtC,aAAa,CAACC,eAAe,CAACe,gBAAgB,CAACuB,OAAO;IACpE;IAEUC,eAAe;QACvB,OAAO,IAAI,CAACxC,aAAa,CAACC,eAAe,CAACwC,SAAS,KAAK;IAC1D;IAEUC,mBAAmB;QAC3B,OAAO;YACL,8DAA8D;YAC9D,CAAC,IAAI,CAAC1C,aAAa,CAACC,eAAe,CAChCL,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAACI,aAAa,CAACC,eAAe,CAACoC,IAAI,CAAC,GAAG,CAAC;QACrE;IACF;IAEUM,sBAAsB;QAC9B,MAAMN,OAAO,IAAI,CAACrC,aAAa,CAACC,eAAe,CAACoC,IAAI;QACpD,OAAO;YACL,CAAC,IAAI,CAACrC,aAAa,CAACC,eAAe,CAACoC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAEA,KAAK,GAAG,CAAC;QAC5D;IACF;IAEUO,kBACRnD,GAAmB,EACnBE,SAAiC,EACjC;QACAf,eAAea,KAAK,aAAa;YAAE,GAAGE,UAAUE,KAAK;QAAC;IACxD;IAEUqC,uBAAuB;YAE3B;QADJ,MAAM,EAAEW,iBAAiB,EAAE,GAAG,IAAI,CAAC7C,aAAa,CAACC,eAAe;QAChE,IAAI,EAAA,mBAAA,IAAI,CAACc,UAAU,qBAAf,iBAAiBI,GAAG,KAAI,CAAC0B,mBAAmB;YAC9C,OAAO;gBACLC,SAAS,CAAC;gBACVC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,gBAAgB,EAAE;gBAClBC,SAAS;oBACPC,eAAe;gBACjB;YACF;QACF;QACA,OAAON;IACT;IAEUO,sBAAsB;QAC9B,OAAO,IAAI,CAACpD,aAAa,CAACC,eAAe,CAACe,gBAAgB,CAACqC,gBAAgB;IAC7E;IA4DUC,WACR7D,GAAmB,EACnBC,GAAoB,EACpBE,QAAgB,EAChBC,KAAyB,EACzBkB,UAA4B,EACL;QACvB,MAAM,EAAEwC,YAAY,EAAE,GAAG,IAAI,CAACvD,aAAa,CAACC,eAAe;QAC3D,IAAI,CAACsD,cAAc;YACjB,MAAM,IAAIzD,MACR;QAEJ;QAEA,kEAAkE;QAClE,8CAA8C;QAC9C,IAAIF,aAAcmB,CAAAA,WAAWI,GAAG,GAAG,eAAe,aAAY,GAAI;YAChEvB,WAAW;QACb;QACA,OAAO2D,aACL9D,KACAC,KACAE,UACAC,OACAM,OAAOW,MAAM,CAACC,YAAY;YACxByC,yBAAyB;YACzBC,SAAS;QACX;IAEJ;IAEA,MAAgBC,iBACdC,IAAoB,EACpBjE,GAAoB,EACpBH,OAMC,EACc;QACfG,IAAIkE,SAAS,CAAC,kBAAkB;QAEhC,yBAAyB;QACzB,iEAAiE;QACjE,IAAIrE,QAAQsE,eAAe,IAAItE,QAAQuE,IAAI,KAAK,QAAQ;YACtDpE,IAAIkE,SAAS,CAAC,gBAAgB;QAChC;QAEA,IAAI,CAAClE,IAAIqE,SAAS,CAAC,iBAAiB;YAClCrE,IAAIkE,SAAS,CACX,gBACArE,QAAQyE,MAAM,CAACC,WAAW,GACtB1E,QAAQyE,MAAM,CAACC,WAAW,GAC1B1E,QAAQuE,IAAI,KAAK,SACjB,qBACA;QAER;QAEA,IAAII;QACJ,IAAI3E,QAAQyE,MAAM,CAACG,SAAS,EAAE;YAC5BD,UAAU3E,QAAQyE,MAAM,CAACI,MAAM,CAAC1E,IAAI2E,eAAe,CAACC,QAAQ;QAC9D,OAAO;YACL,MAAMC,UAAUhF,QAAQyE,MAAM,CAACQ,iBAAiB;YAChD9E,IAAIkE,SAAS,CAAC,kBAAkBa,OAAOjG,WAAW+F;YAClD,IAAIhF,QAAQmF,aAAa,EAAE;gBACzBhF,IAAIkE,SAAS,CAAC,QAAQjF,aAAa4F;YACrC;YACA7E,IAAIiF,IAAI,CAACJ;QACX;QAEA7E,IAAIkF,IAAI;QAER,gDAAgD;QAChD,IAAIV,SAAS,MAAMA;IACrB;IAEA,MAAgBW,mBAAmB,EACjCxC,IAAI,EACJxC,KAAK,EACLiF,MAAM,EAMP,EAAE;QACD,MAAMd,SAAS,MAAM,IAAI,CAAChE,aAAa,CAACC,eAAe,CAAC8E,aAAa,CAAC1C;QACtE,IAAI,CAAC2B,QAAQ,OAAO;QAEpB,OAAO;YACLnE,OAAO;gBACL,GAAIA,SAAS,CAAC,CAAC;gBACf,GAAIiF,UAAU,CAAC,CAAC;YAClB;YACAE,YAAYhB;QACd;IACF;IAEA,2EAA2E;IAC3E,+DAA+D;IAE/D,MAAgBiB,SAAS;QACvB,wDAAwD;QACxD,OAAO;IACT;IAEA,MAAgBC,mBAAmB;QACjC,4DAA4D;QAC5D,OAAO;IACT;IAEUC,gBAAgB;IACxB,2EAA2E;IAC3E,mBAAmB;IACrB;IAEUC,eAAe;QACvB,kDAAkD;QAClD,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAO;IACT;IAEA,MAAgBC,cAAc;QAC5B,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAOC;IACT;IAEUC,oBAAoB;IAC5B,wEAAwE;IACxE,4EAA4E;IAC9E;IAEA,MAAgBC,gBAA+B;IAC7C,+CAA+C;IACjD;IAEA,MAAgBC,6BAAuE;QACrF,wEAAwE;QACxE,OAAO;IACT;IAEUC,oBAAyD;QACjE,4EAA4E;QAC5E,gDAAgD;QAChD,OAAOJ;IACT;IAEUK,gBAAmD;QAC3D,yEAAyE;QACzE,gDAAgD;QAChD,OAAOL;IACT;IAEUM,qBAAqB;QAC7B,OAAO,IAAIC;IACb;IAEA,MAAgBC,iBAAyC;QACvD,OAAO;IACT;AACF"}