"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AfriColaWebsite; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction AfriColaWebsite() {\n    _s();\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const canRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const heroRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setScrollY(window.scrollY);\n            // Advanced parallax for can\n            if (canRef.current) {\n                const scrolled = window.pageYOffset;\n                const rate = scrolled * -0.3;\n                const rotate = scrolled * 0.1;\n                canRef.current.style.transform = \"translateY(\".concat(rate, \"px) rotateY(\").concat(rotate, \"deg)\");\n            }\n        };\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX,\n                y: e.clientY\n            });\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n            window.removeEventListener(\"mousemove\", handleMouseMove);\n        };\n    }, []);\n    const scrollToSection = (sectionId)=>{\n        var _document_getElementById;\n        (_document_getElementById = document.getElementById(sectionId)) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 text-white overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed w-6 h-6 bg-gradient-to-r from-orange-400 to-amber-400 rounded-full pointer-events-none z-50 mix-blend-screen transition-all duration-300 ease-out opacity-70\",\n                style: {\n                    left: mousePosition.x - 12,\n                    top: mousePosition.y - 12,\n                    transform: \"scale(\".concat(1 + Math.sin(Date.now() * 0.003) * 0.2, \")\")\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 w-full z-40 bg-slate-900/80 backdrop-blur-xl border-b border-orange-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl font-black bg-gradient-to-r from-orange-400 via-amber-400 to-orange-500 bg-clip-text text-transparent\",\n                                children: \"AFRICOLA\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex space-x-8\",\n                                children: [\n                                    \"HOME\",\n                                    \"PRODOTTO\",\n                                    \"ENERGIA\",\n                                    \"ACQUISTA\",\n                                    \"CONTATTI\"\n                                ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToSection(item.toLowerCase()),\n                                        className: \"relative group text-white hover:text-orange-400 font-semibold text-sm tracking-wide transition-all duration-300\",\n                                        children: [\n                                            item,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-orange-400 to-amber-400 group-hover:w-full transition-all duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"home\",\n                ref: heroRef,\n                className: \"relative min-h-screen flex items-center justify-center overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-blue-900/50 via-slate-900/80 to-orange-900/30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-orange-400/20 to-amber-400/20 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full blur-3xl animate-pulse delay-1000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 max-w-7xl mx-auto px-6 grid lg:grid-cols-2 gap-16 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center lg:text-left space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-center space-x-2 bg-gradient-to-r from-orange-500/20 to-amber-500/20 backdrop-blur-sm border border-orange-400/30 px-6 py-3 rounded-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"w-2 h-2 bg-orange-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-orange-400 font-bold text-sm tracking-wider\",\n                                                        children: \"MEDITERRANEAN ENERGY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"w-2 h-2 bg-orange-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-6xl md:text-8xl font-black leading-none\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block bg-gradient-to-r from-white via-orange-200 to-white bg-clip-text text-transparent\",\n                                                        children: \"L'ENERGIA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block bg-gradient-to-r from-orange-400 via-amber-400 to-orange-500 bg-clip-text text-transparent animate-pulse\",\n                                                        children: \"CHE NASCE DAL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block bg-gradient-to-r from-blue-400 via-cyan-400 to-blue-500 bg-clip-text text-transparent\",\n                                                        children: \"MEDITERRANEO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl md:text-2xl text-slate-300 leading-relaxed max-w-2xl\",\n                                                children: [\n                                                    \"AfriCola Energy non \\xe8 una semplice bevanda: \\xe8 un'\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-orange-400 font-bold\",\n                                                        children: \"esperienza autentica\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \" che unisce la forza della Tunisia con l'eleganza italiana.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg text-slate-400 leading-relaxed max-w-xl\",\n                                                children: \"Ogni sorso ti trasporta nel cuore del Mediterraneo, dove tradizione e innovazione si incontrano per creare energia pura.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"group relative overflow-hidden bg-gradient-to-r from-orange-500 to-amber-500 text-white px-10 py-4 text-lg font-bold rounded-2xl hover:shadow-2xl hover:shadow-orange-500/50 transition-all duration-500 transform hover:scale-105\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"relative z-10 flex items-center justify-center\",\n                                                        children: [\n                                                            \"PROVALA ORA\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-3 group-hover:translate-x-1 transition-transform\",\n                                                                children: \"→\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 129,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-amber-500 to-orange-500 translate-x-full group-hover:translate-x-0 transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"group border-2 border-orange-400 text-orange-400 px-10 py-4 text-lg font-bold rounded-2xl hover:bg-orange-400 hover:text-white transition-all duration-300 backdrop-blur-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center justify-center\",\n                                                    children: [\n                                                        \"SCOPRI DI PI\\xd9\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-3 group-hover:rotate-45 transition-transform\",\n                                                            children: \"↗\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-96 h-96 border border-orange-400/30 rounded-full animate-spin-slow\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute w-80 h-80 border border-blue-400/20 rounded-full animate-spin-slow-reverse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute w-64 h-64 border border-amber-400/40 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: canRef,\n                                            className: \"relative z-10 transform-gpu\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-orange-400/30 via-transparent to-blue-400/30 rounded-full blur-2xl scale-150 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"/africola-can.png\",\n                                                    alt: \"AfriCola Energy Can\",\n                                                    className: \"relative z-10 w-80 md:w-96 h-auto drop-shadow-2xl hover:scale-110 transition-all duration-700 transform-gpu\",\n                                                    style: {\n                                                        filter: \"drop-shadow(0 0 40px rgba(251, 146, 60, 0.4)) drop-shadow(0 0 80px rgba(59, 130, 246, 0.2))\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-10 -left-8 w-4 h-4 bg-gradient-to-r from-orange-400 to-amber-400 rounded-full animate-bounce delay-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-20 -right-6 w-3 h-3 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full animate-bounce delay-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-32 right-16 w-2 h-2 bg-gradient-to-r from-amber-400 to-orange-500 rounded-full animate-bounce delay-1000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-2 animate-bounce\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-slate-400 text-sm font-medium\",\n                                    children: \"Scorri per scoprire\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-10 border-2 border-orange-400/50 rounded-full flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-3 bg-gradient-to-b from-orange-400 to-transparent rounded-full mt-2 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"prodotto\",\n                className: \"py-32 bg-gradient-to-b from-slate-900 to-blue-900 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(251,146,60,0.1)_0%,_transparent_70%)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-6 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-orange-400 to-amber-400 bg-clip-text text-transparent text-sm font-bold tracking-[0.3em] uppercase\",\n                                            children: \"⚡ Formula Perfetta ⚡\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-5xl md:text-7xl font-black mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent\",\n                                                children: \"LA NOSTRA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block bg-gradient-to-r from-orange-400 to-amber-400 bg-clip-text text-transparent\",\n                                                children: \"FORMULA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed\",\n                                        children: \"Ingredienti selezionati dal Mediterraneo per un'esperienza energetica autentica e duratura\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20\",\n                                children: [\n                                    {\n                                        icon: \"⚡\",\n                                        title: \"CAFFEINA NATURALE\",\n                                        desc: \"160mg di energia pura\",\n                                        gradient: \"from-orange-400 to-red-500\",\n                                        border: \"border-orange-400/30\"\n                                    },\n                                    {\n                                        icon: \"\\uD83C\\uDF30\",\n                                        title: \"NOCE DI COLA\",\n                                        desc: \"Estratto autentico africano\",\n                                        gradient: \"from-amber-400 to-orange-500\",\n                                        border: \"border-amber-400/30\"\n                                    },\n                                    {\n                                        icon: \"\\uD83C\\uDF0A\",\n                                        title: \"SPIRITO MEDITERRANEO\",\n                                        desc: \"Tradizione e innovazione\",\n                                        gradient: \"from-blue-400 to-cyan-500\",\n                                        border: \"border-blue-400/30\"\n                                    },\n                                    {\n                                        icon: \"✨\",\n                                        title: \"QUALIT\\xc0 PREMIUM\",\n                                        desc: \"Standard elevati italiani\",\n                                        gradient: \"from-purple-400 to-pink-500\",\n                                        border: \"border-purple-400/30\"\n                                    }\n                                ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group relative bg-slate-800/50 backdrop-blur-sm p-8 rounded-3xl border \".concat(feature.border, \" hover:border-opacity-60 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br \".concat(feature.gradient, \" opacity-0 group-hover:opacity-10 rounded-3xl transition-all duration-500\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-5xl mb-6 group-hover:scale-125 transition-transform duration-500\",\n                                                        children: feature.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white mb-3 leading-tight\",\n                                                        children: feature.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: feature.desc\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-1 bg-gradient-to-r \".concat(feature.gradient, \" mx-auto mt-4 rounded-full group-hover:w-20 transition-all duration-500\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-gradient-to-r from-slate-800/50 via-blue-900/30 to-slate-800/50 backdrop-blur-sm rounded-3xl p-12 border border-orange-400/20 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-orange-400/5 via-transparent to-blue-400/5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10 grid lg:grid-cols-2 gap-12 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-4xl md:text-5xl font-black mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-gradient-to-r from-orange-400 to-amber-400 bg-clip-text text-transparent\",\n                                                                        children: \"ENERGIA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: \"AUTENTICA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl text-slate-300 leading-relaxed mb-8\",\n                                                                children: \"Ogni lattina racchiude l'essenza del Mediterraneo: la forza della Tunisia e la raffinatezza dell'Italia in un'esperienza energetica unica.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-6\",\n                                                        children: [\n                                                            {\n                                                                label: \"Caffeina\",\n                                                                value: \"160mg\"\n                                                            },\n                                                            {\n                                                                label: \"Calorie\",\n                                                                value: \"45\"\n                                                            },\n                                                            {\n                                                                label: \"Zuccheri\",\n                                                                value: \"0g\"\n                                                            },\n                                                            {\n                                                                label: \"Volume\",\n                                                                value: \"330ml\"\n                                                            }\n                                                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-4 bg-slate-700/30 rounded-2xl border border-slate-600/30\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-black text-orange-400\",\n                                                                        children: stat.value\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-slate-400 text-sm\",\n                                                                        children: stat.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            \"Estratto naturale di noce di cola africana\",\n                                                            \"Formula energetica bilanciata e duratura\",\n                                                            \"Gusto intenso e rinfrescante\",\n                                                            \"Zero zuccheri aggiunti, massima purezza\"\n                                                        ].map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-gradient-to-r from-orange-400 to-amber-400 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-slate-300\",\n                                                                        children: benefit\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-r from-orange-400/20 to-blue-400/20 rounded-full blur-3xl scale-150 animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"/africola-can.png\",\n                                                            alt: \"AfriCola Energy Premium\",\n                                                            className: \"relative z-10 w-72 h-auto drop-shadow-2xl hover:scale-110 transition-transform duration-700\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"energia\",\n                className: \"py-32 bg-gradient-to-b from-blue-900 to-slate-900 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(59,130,246,0.1)_0%,_transparent_70%)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-6 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-5xl md:text-7xl font-black mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n                                                children: \"UN PONTE TRA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block bg-gradient-to-r from-orange-400 to-amber-400 bg-clip-text text-transparent\",\n                                                children: \"DUE CULTURE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-slate-300 max-w-4xl mx-auto leading-relaxed\",\n                                        children: \"Nel cuore del Mediterraneo, dove Africa ed Europa si incontrano, nasce AfriCola Energy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-3 gap-12 items-center mb-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group text-center space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative mx-auto w-32 h-32 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center text-5xl group-hover:scale-110 transition-transform duration-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-br from-red-400/50 to-transparent rounded-full blur-xl group-hover:blur-2xl transition-all duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"relative z-10\",\n                                                        children: \"\\uD83C\\uDDF9\\uD83C\\uDDF3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-3xl font-black text-red-400 mb-3\",\n                                                        children: \"TUNISIA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-300 leading-relaxed\",\n                                                        children: \"La forza dell'Africa, la vitalit\\xe0 del deserto, l'energia del sole mediterraneo che dona vita alla noce di cola.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group text-center space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative mx-auto w-40 h-40 bg-gradient-to-br from-blue-500 via-cyan-400 to-blue-600 rounded-full flex items-center justify-center text-6xl group-hover:scale-110 transition-transform duration-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-br from-blue-400/50 to-cyan-400/30 rounded-full blur-xl group-hover:blur-2xl transition-all duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"relative z-10\",\n                                                        children: \"\\uD83C\\uDF0A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-4xl font-black text-cyan-400 mb-4\",\n                                                        children: \"MEDITERRANEO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-300 leading-relaxed text-lg\",\n                                                        children: \"Il nostro mare, ponte tra continenti, custode di tradizioni millenarie e innovazione moderna.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group text-center space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative mx-auto w-32 h-32 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center text-5xl group-hover:scale-110 transition-transform duration-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-br from-green-400/50 to-transparent rounded-full blur-xl group-hover:blur-2xl transition-all duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"relative z-10\",\n                                                        children: \"\\uD83C\\uDDEE\\uD83C\\uDDF9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-3xl font-black text-green-400 mb-3\",\n                                                        children: \"ITALIA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-300 leading-relaxed\",\n                                                        children: \"L'arte del design, la passione per la qualit\\xe0, l'eleganza che trasforma ogni prodotto in un'opera d'arte.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-3 gap-8\",\n                                children: [\n                                    {\n                                        title: \"AUTENTICIT\\xc0\",\n                                        desc: \"Ingredienti naturali selezionati dalle migliori fonti mediterranee\",\n                                        icon: \"\\uD83C\\uDF3F\",\n                                        gradient: \"from-green-400 to-emerald-500\"\n                                    },\n                                    {\n                                        title: \"TRADIZIONE\",\n                                        desc: \"Ricette che rispettano la storia e la cultura del nostro territorio\",\n                                        icon: \"\\uD83C\\uDFDB️\",\n                                        gradient: \"from-amber-400 to-orange-500\"\n                                    },\n                                    {\n                                        title: \"INNOVAZIONE\",\n                                        desc: \"Tecnologie moderne per preservare la purezza degli ingredienti\",\n                                        icon: \"⚡\",\n                                        gradient: \"from-blue-400 to-purple-500\"\n                                    }\n                                ].map((value, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group text-center p-8 bg-slate-800/30 backdrop-blur-sm rounded-3xl border border-slate-600/30 hover:border-orange-400/50 transition-all duration-500 hover:transform hover:scale-105\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-5xl mb-6 group-hover:scale-125 transition-transform duration-500\",\n                                                children: value.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-black mb-4 bg-gradient-to-r \".concat(value.gradient, \" bg-clip-text text-transparent\"),\n                                                children: value.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-400 leading-relaxed\",\n                                                children: value.desc\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"acquista\",\n                className: \"py-32 bg-gradient-to-b from-slate-900 to-blue-900 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(251,146,60,0.1)_0%,_transparent_70%)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-6 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-5xl md:text-7xl font-black mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent\",\n                                                children: \"SCEGLI LA TUA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block bg-gradient-to-r from-orange-400 to-amber-400 bg-clip-text text-transparent\",\n                                                children: \"ESPERIENZA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-slate-300 max-w-3xl mx-auto\",\n                                        children: \"Energia mediterranea disponibile in confezioni pensate per ogni momento della tua giornata\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-3 gap-8 mb-16\",\n                                children: [\n                                    {\n                                        name: \"ASSAGGIO\",\n                                        quantity: \"1 LATTINA\",\n                                        price: \"2.50\",\n                                        desc: \"Perfetta per scoprire il gusto\",\n                                        features: [\n                                            \"Prima esperienza\",\n                                            \"Gusto autentico\",\n                                            \"Energia immediata\"\n                                        ],\n                                        popular: false,\n                                        gradient: \"from-slate-700 to-slate-800\",\n                                        border: \"border-slate-600\"\n                                    },\n                                    {\n                                        name: \"ENERGIA\",\n                                        quantity: \"6 LATTINE\",\n                                        price: \"14.00\",\n                                        desc: \"Ideale per la settimana\",\n                                        features: [\n                                            \"Risparmio garantito\",\n                                            \"Energia costante\",\n                                            \"Confezione pratica\"\n                                        ],\n                                        popular: true,\n                                        gradient: \"from-orange-500/20 to-amber-500/20\",\n                                        border: \"border-orange-400\"\n                                    },\n                                    {\n                                        name: \"POTENZA\",\n                                        quantity: \"12 LATTINE\",\n                                        price: \"26.00\",\n                                        desc: \"Per i veri intenditori\",\n                                        features: [\n                                            \"Massimo risparmio\",\n                                            \"Scorta mensile\",\n                                            \"Qualit\\xe0 premium\"\n                                        ],\n                                        popular: false,\n                                        gradient: \"from-blue-500/20 to-cyan-500/20\",\n                                        border: \"border-blue-400\"\n                                    }\n                                ].map((pack, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group relative bg-gradient-to-br \".concat(pack.gradient, \" backdrop-blur-sm p-8 rounded-3xl border \").concat(pack.border, \" transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl \").concat(pack.popular ? \"scale-105 shadow-xl shadow-orange-500/20\" : \"\"),\n                                        children: [\n                                            pack.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-6 left-1/2 transform -translate-x-1/2 z-20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-orange-500 to-amber-500 text-white px-6 py-3 rounded-full text-sm font-black tracking-wider animate-pulse\",\n                                                    children: \"⚡ PI\\xd9 SCELTO ⚡\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-bold text-orange-400 tracking-wider mb-2\",\n                                                                children: pack.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-black text-white mb-2\",\n                                                                children: pack.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-slate-400\",\n                                                                children: pack.desc\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-5xl font-black text-white\",\n                                                                children: [\n                                                                    pack.price,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xl text-orange-400 ml-2\",\n                                                                        children: \"TND\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 508,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-2\",\n                                                                children: pack.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        className: \"flex items-center justify-center text-sm text-slate-300\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"w-1.5 h-1.5 bg-orange-400 rounded-full mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 514,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            feature\n                                                                        ]\n                                                                    }, idx, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 513,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 511,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"w-full bg-gradient-to-r from-orange-500 to-amber-500 text-white py-4 rounded-2xl font-bold text-lg hover:shadow-2xl hover:shadow-orange-500/50 transition-all duration-500 transform hover:scale-105\",\n                                                        children: \"ORDINA ORA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center space-x-4 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 backdrop-blur-sm border border-blue-400/30 px-8 py-4 rounded-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83D\\uDE9A\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-400 font-bold text-lg\",\n                                                children: \"Spedizione gratuita in tutta la Tunisia\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"group bg-transparent border-2 border-orange-400 text-orange-400 px-10 py-4 rounded-2xl font-bold text-lg hover:bg-orange-400 hover:text-white transition-all duration-500 backdrop-blur-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                \"\\uD83D\\uDCCD TROVA IL PUNTO VENDITA PI\\xd9 VICINO\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-3 group-hover:translate-x-2 transition-transform\",\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 438,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 435,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"contatti\",\n                className: \"py-32 bg-gradient-to-b from-blue-900 to-slate-900 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-5xl md:text-7xl font-black mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block bg-gradient-to-r from-orange-400 to-amber-400 bg-clip-text text-transparent\",\n                                            children: \"UNISCITI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n                                            children: \"ALLA FAMIGLIA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-slate-300 max-w-4xl mx-auto leading-relaxed\",\n                                    children: \"Diventa parte della rivoluzione energetica mediterranea. Contattaci per partnership e collaborazioni.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                            lineNumber: 548,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-2 gap-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        {\n                                            icon: \"\\uD83D\\uDCE7\",\n                                            title: \"EMAIL\",\n                                            info: \"<EMAIL>\",\n                                            gradient: \"from-orange-400 to-amber-400\",\n                                            bg: \"from-orange-500/20 to-amber-500/20\"\n                                        },\n                                        {\n                                            icon: \"\\uD83D\\uDCF1\",\n                                            title: \"TELEFONO\",\n                                            info: \"+216 XX XXX XXX\",\n                                            gradient: \"from-blue-400 to-cyan-400\",\n                                            bg: \"from-blue-500/20 to-cyan-500/20\"\n                                        },\n                                        {\n                                            icon: \"\\uD83D\\uDCCD\",\n                                            title: \"SEDE\",\n                                            info: \"Tunis, Tunisia\",\n                                            gradient: \"from-purple-400 to-pink-400\",\n                                            bg: \"from-purple-500/20 to-pink-500/20\"\n                                        }\n                                    ].map((contact, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group flex items-center space-x-6 p-6 bg-gradient-to-r \".concat(contact.bg, \" backdrop-blur-sm rounded-3xl border border-slate-600/30 hover:border-orange-400/50 transition-all duration-500 hover:transform hover:scale-105\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-r \".concat(contact.gradient, \" rounded-full flex items-center justify-center text-2xl text-white group-hover:scale-110 transition-transform duration-500\"),\n                                                    children: contact.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-black bg-gradient-to-r \".concat(contact.gradient, \" bg-clip-text text-transparent\"),\n                                                            children: contact.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-300 text-lg\",\n                                                            children: contact.info\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-slate-800/50 to-blue-900/30 backdrop-blur-sm p-10 rounded-3xl border border-orange-400/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-3xl font-black bg-gradient-to-r from-orange-400 to-amber-400 bg-clip-text text-transparent mb-8 text-center\",\n                                            children: \"DIVENTA PARTNER\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Nome\",\n                                                            className: \"bg-slate-700/50 border border-slate-600/50 rounded-2xl px-6 py-4 text-white placeholder-slate-400 focus:border-orange-400 focus:outline-none transition-all duration-300 backdrop-blur-sm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Cognome\",\n                                                            className: \"bg-slate-700/50 border border-slate-600/50 rounded-2xl px-6 py-4 text-white placeholder-slate-400 focus:border-orange-400 focus:outline-none transition-all duration-300 backdrop-blur-sm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    placeholder: \"Email\",\n                                                    className: \"w-full bg-slate-700/50 border border-slate-600/50 rounded-2xl px-6 py-4 text-white placeholder-slate-400 focus:border-orange-400 focus:outline-none transition-all duration-300 backdrop-blur-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Nome Attivit\\xe0\",\n                                                    className: \"w-full bg-slate-700/50 border border-slate-600/50 rounded-2xl px-6 py-4 text-white placeholder-slate-400 focus:border-orange-400 focus:outline-none transition-all duration-300 backdrop-blur-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    placeholder: \"Raccontaci della tua attivit\\xe0 e dei tuoi obiettivi...\",\n                                                    rows: 4,\n                                                    className: \"w-full bg-slate-700/50 border border-slate-600/50 rounded-2xl px-6 py-4 text-white placeholder-slate-400 focus:border-orange-400 focus:outline-none resize-none transition-all duration-300 backdrop-blur-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    className: \"w-full bg-gradient-to-r from-orange-500 to-amber-500 text-white py-4 rounded-2xl font-bold text-lg hover:shadow-2xl hover:shadow-orange-500/50 transition-all duration-500 transform hover:scale-105\",\n                                                    children: \"INVIA RICHIESTA ⚡\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 640,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                            lineNumber: 562,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                    lineNumber: 547,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 546,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gradient-to-t from-slate-900 to-blue-900 border-t border-orange-400/20 py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-black bg-gradient-to-r from-orange-400 via-amber-400 to-orange-500 bg-clip-text text-transparent\",\n                                        children: \"AFRICOLA ENERGY\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-slate-400\",\n                                        children: \"L'energia che nasce dal Mediterraneo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 656,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center space-x-8 text-slate-500 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\xa9 2024 AfriCola Energy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-orange-400\",\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Made in Tunisia \\uD83C\\uDDF9\\uD83C\\uDDF3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-orange-400\",\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Designed with Italian Passion \\uD83C\\uDDEE\\uD83C\\uDDF9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 663,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-gradient-to-r from-orange-400 to-amber-400 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full animate-pulse delay-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse delay-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 671,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                    lineNumber: 654,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 653,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_s(AfriColaWebsite, \"Rp0K+udvWARW+2xzetEiQ8Xpqbg=\");\n_c = AfriColaWebsite;\nvar _c;\n$RefreshReg$(_c, \"AfriColaWebsite\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});