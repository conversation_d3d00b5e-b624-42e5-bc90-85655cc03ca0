@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@400;500;600;700;800;900&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Inter", sans-serif;
  background: #000;
  color: #fff;
  overflow-x: hidden;
  line-height: 1.6;
}

/* Premium Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

/* Smooth animations */
* {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #111;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #ffd700 0%, #ffb300 100%);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #ffb300 0%, #ffd700 100%);
}

/* Backdrop blur support */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  backdrop-filter: blur(12px);
}

.backdrop-blur-xl {
  backdrop-filter: blur(24px);
}

/* Transform GPU acceleration */
.transform-gpu {
  transform: translateZ(0);
  will-change: transform;
}

/* Focus states */
input:focus,
textarea:focus,
button:focus,
select:focus {
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.3);
}

/* Text selection */
::selection {
  background: rgba(255, 215, 0, 0.3);
  color: white;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  h1 {
    font-size: 3rem !important;
  }

  h2 {
    font-size: 2.5rem !important;
  }

  .text-8xl {
    font-size: 4rem !important;
  }

  .text-9xl {
    font-size: 5rem !important;
  }

  .text-7xl {
    font-size: 3.5rem !important;
  }

  .text-6xl {
    font-size: 3rem !important;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus visible for keyboard navigation */
button:focus-visible,
a:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid #ffd700;
  outline-offset: 2px;
}

/* Gradient text effects */
.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

/* Performance optimizations */
img {
  will-change: transform;
}

.transform {
  will-change: transform;
}

/* Smooth transitions for all interactive elements */
button,
a,
input,
textarea,
select,
.group {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Container max width */
.container {
  max-width: 1200px;
}

/* Custom animations for floating elements */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Glow effects */
.glow {
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.glow-hover:hover {
  box-shadow: 0 0 30px rgba(255, 215, 0, 0.7);
}
