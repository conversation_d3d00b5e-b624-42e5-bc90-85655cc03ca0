@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";
@import url("https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Montserrat:wght@400;600;700;900&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Montserrat", sans-serif;
  background: #000;
  color: #fff;
  overflow-x: hidden;
}

h1,
h2,
h3 {
  font-family: "Bebas Neue", cursive;
  letter-spacing: 0.05em;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px #ffcc00;
  }
  to {
    box-shadow: 0 0 30px #ffcc00, 0 0 40px #ffcc00;
  }
}

.energy-pulse {
  animation: energyPulse 1.5s ease-in-out infinite;
}

@keyframes energyPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #111;
}

::-webkit-scrollbar-thumb {
  background: #ffcc00;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #ffd700;
}

/* Smooth transitions for all interactive elements */
button,
a,
input,
textarea {
  transition: all 0.3s ease;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem !important;
  }

  h2 {
    font-size: 2rem !important;
  }

  .hero-can {
    width: 250px !important;
  }
}
