{"version": 3, "sources": ["../../../src/server/lib/utils.ts"], "names": ["printAndExit", "message", "code", "console", "log", "error", "process", "exit", "getDebugPort", "debugPortStr", "execArgv", "find", "localArg", "startsWith", "split", "env", "NODE_OPTIONS", "match", "parseInt", "NODE_INSPECT_RE", "getNodeOptionsWithoutInspect", "replace", "getPort", "args", "parsed", "PORT", "Number", "isNaN", "RESTART_EXIT_CODE", "checkNodeDebugType", "nodeDebugType", "undefined", "some", "getMaxOldSpaceSize", "maxOldSpaceSize"], "mappings": "AAEA,OAAO,SAASA,aAAaC,OAAe,EAAEC,OAAO,CAAC;IACpD,IAAIA,SAAS,GAAG;QACdC,QAAQC,GAAG,CAACH;IACd,OAAO;QACLE,QAAQE,KAAK,CAACJ;IAChB;IAEAK,QAAQC,IAAI,CAACL;AACf;AAEA,OAAO,MAAMM,eAAe;QAExBF,wBAOAA,iCAAAA,kCAAAA;IARF,MAAMG,eACJH,EAAAA,yBAAAA,QAAQI,QAAQ,CACbC,IAAI,CACH,CAACC,WACCA,SAASC,UAAU,CAAC,gBACpBD,SAASC,UAAU,CAAC,sCAJ1BP,uBAMIQ,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,OACpBR,4BAAAA,QAAQS,GAAG,CAACC,YAAY,sBAAxBV,mCAAAA,0BAA0BW,KAAK,sBAA/BX,kCAAAA,sCAAAA,2BAAkC,sDAAlCA,+BAAqE,CAAC,EAAE;IAC1E,OAAOG,eAAeS,SAAST,cAAc,MAAM;AACrD,EAAC;AAED,MAAMU,kBAAkB;AACxB,OAAO,SAASC;IACd,OAAO,AAACd,CAAAA,QAAQS,GAAG,CAACC,YAAY,IAAI,EAAC,EAAGK,OAAO,CAACF,iBAAiB;AACnE;AAEA,OAAO,SAASG,QAAQC,IAA0B;IAChD,IAAI,OAAOA,IAAI,CAAC,SAAS,KAAK,UAAU;QACtC,OAAOA,IAAI,CAAC,SAAS;IACvB;IAEA,MAAMC,SAASlB,QAAQS,GAAG,CAACU,IAAI,IAAIP,SAASZ,QAAQS,GAAG,CAACU,IAAI,EAAE;IAC9D,IAAI,OAAOD,WAAW,YAAY,CAACE,OAAOC,KAAK,CAACH,SAAS;QACvD,OAAOA;IACT;IAEA,OAAO;AACT;AAEA,OAAO,MAAMI,oBAAoB,GAAE;AAEnC,OAAO,SAASC;QAKZvB,iCAAAA,2BAOAA,kCAAAA;IAXF,IAAIwB,gBAAgBC;IAEpB,IACEzB,QAAQI,QAAQ,CAACsB,IAAI,CAAC,CAACpB,WAAaA,SAASC,UAAU,CAAC,mBACxDP,4BAAAA,QAAQS,GAAG,CAACC,YAAY,sBAAxBV,kCAAAA,0BAA0BW,KAAK,qBAA/BX,qCAAAA,2BAAkC,2BAClC;QACAwB,gBAAgB;IAClB;IAEA,IACExB,QAAQI,QAAQ,CAACsB,IAAI,CAAC,CAACpB,WAAaA,SAASC,UAAU,CAAC,uBACxDP,6BAAAA,QAAQS,GAAG,CAACC,YAAY,sBAAxBV,mCAAAA,2BAA0BW,KAAK,qBAA/BX,sCAAAA,4BAAkC,+BAClC;QACAwB,gBAAgB;IAClB;IAEA,OAAOA;AACT;AAEA,OAAO,SAASG;QACU3B,iCAAAA;IAAxB,MAAM4B,mBAAkB5B,4BAAAA,QAAQS,GAAG,CAACC,YAAY,sBAAxBV,kCAAAA,0BAA0BW,KAAK,CACrD,kDADsBX,+BAErB,CAAC,EAAE;IAEN,OAAO4B,kBAAkBhB,SAASgB,iBAAiB,MAAMH;AAC3D"}