{"version": 3, "sources": ["../../../src/lib/eslint/writeOutputFile.ts"], "names": ["promises", "fs", "path", "Log", "isError", "isDirectory", "filePath", "stat", "then", "catch", "error", "code", "writeOutputFile", "outputFile", "outputData", "resolve", "process", "cwd", "mkdir", "dirname", "recursive", "writeFile", "info", "err", "console"], "mappings": "AAAA,SAASA,YAAYC,EAAE,QAAQ,KAAI;AACnC,OAAOC,UAAU,OAAM;AACvB,YAAYC,SAAS,yBAAwB;AAC7C,OAAOC,aAAa,qBAAoB;AAExC;;;CAGC,GACD,SAASC,YACP,kCAAkC,GAClCC,QAAgB;IAEhB,OAAOL,GACJM,IAAI,CAACD,UACLE,IAAI,CAAC,CAACD,OAASA,KAAKF,WAAW,IAC/BI,KAAK,CAAC,CAACC;QACN,IACEN,QAAQM,UACPA,CAAAA,MAAMC,IAAI,KAAK,YAAYD,MAAMC,IAAI,KAAK,SAAQ,GACnD;YACA,OAAO;QACT;QACA,MAAMD;IACR;AACJ;AACA;;CAEC,GACD,OAAO,eAAeE,gBACpB,2CAA2C,GAC3CC,UAAkB,EAClB,qDAAqD,GACrDC,UAAkB;IAElB,MAAMR,WAAWJ,KAAKa,OAAO,CAACC,QAAQC,GAAG,IAAIJ;IAE7C,IAAI,MAAMR,YAAYC,WAAW;QAC/BH,IAAIO,KAAK,CACP,CAAC,qDAAqD,EAAEJ,SAAS,CAAC;IAEtE,OAAO;QACL,IAAI;YACF,MAAML,GAAGiB,KAAK,CAAChB,KAAKiB,OAAO,CAACb,WAAW;gBAAEc,WAAW;YAAK;YACzD,MAAMnB,GAAGoB,SAAS,CAACf,UAAUQ;YAC7BX,IAAImB,IAAI,CAAC,CAAC,kCAAkC,EAAEhB,SAAS,CAAC;QAC1D,EAAE,OAAOiB,KAAK;YACZpB,IAAIO,KAAK,CAAC,CAAC,6CAA6C,EAAEJ,SAAS,CAAC;YACpEkB,QAAQd,KAAK,CAACa;QAChB;IACF;AACF"}