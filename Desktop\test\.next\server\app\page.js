/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Capp%5Cglobals.css&server=true!":
/*!**************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Capp%5Cglobals.css&server=true! ***!
  \**************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Capp%5Cpage.tsx&server=true!":
/*!***********************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Capp%5Cpage.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDTUVHQS1QQyU1Q0Rlc2t0b3AlNUN0ZXN0JTVDYXBwJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWZyaWNvbGEtZW5lcmd5LXdlYnNpdGUvP2FkNDAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxNRUdBLVBDXFxcXERlc2t0b3BcXFxcdGVzdFxcXFxhcHBcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AfriColaWebsite)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AfriColaWebsite() {\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const canRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const heroRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setScrollY(window.scrollY);\n            // Premium parallax for can\n            if (canRef.current) {\n                const scrolled = window.pageYOffset;\n                const rate = scrolled * -0.3;\n                const rotate = scrolled * 0.1;\n                canRef.current.style.transform = `translateY(${rate}px) rotateY(${rotate}deg) rotateX(${rotate * 0.5}deg)`;\n            }\n        };\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX,\n                y: e.clientY\n            });\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n            window.removeEventListener(\"mousemove\", handleMouseMove);\n        };\n    }, []);\n    const scrollToSection = (sectionId)=>{\n        document.getElementById(sectionId)?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black text-white overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed w-8 h-8 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full pointer-events-none z-50 mix-blend-screen transition-all duration-300 ease-out opacity-60\",\n                style: {\n                    left: mousePosition.x - 16,\n                    top: mousePosition.y - 16,\n                    transform: `scale(${1 + Math.sin(Date.now() * 0.003) * 0.3})`\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 w-full z-40 bg-black/90 backdrop-blur-xl border-b border-yellow-400/30\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl font-black bg-gradient-to-r from-yellow-400 via-amber-400 to-yellow-500 bg-clip-text text-transparent tracking-tight\",\n                                children: \"AFRICOLA\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex space-x-8\",\n                                children: [\n                                    \"HOME\",\n                                    \"PRODOTTO\",\n                                    \"ENERGIA\",\n                                    \"ACQUISTA\",\n                                    \"CONTATTI\"\n                                ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToSection(item.toLowerCase()),\n                                        className: \"relative group text-white hover:text-yellow-400 font-bold text-sm tracking-wide transition-all duration-300\",\n                                        children: [\n                                            item,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-yellow-400 to-amber-500 group-hover:w-full transition-all duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"home\",\n                ref: heroRef,\n                className: \"relative min-h-screen flex items-center justify-center overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-yellow-400/20 to-amber-400/20 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-amber-400/15 to-yellow-400/15 rounded-full blur-3xl animate-pulse delay-1000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(255,215,0,0.1)_0%,_transparent_70%)]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 max-w-7xl mx-auto px-6 grid lg:grid-cols-2 gap-16 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center lg:text-left space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-center space-x-3 bg-gradient-to-r from-yellow-400/20 to-amber-400/20 backdrop-blur-sm border border-yellow-400/40 px-8 py-4 rounded-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"w-2 h-2 bg-yellow-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-black text-sm tracking-[0.2em] uppercase\",\n                                                        children: \"MEDITERRANEAN ENERGY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"w-2 h-2 bg-yellow-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-6xl md:text-8xl font-black leading-none\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block text-white drop-shadow-2xl\",\n                                                        children: \"L'ENERGIA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block bg-gradient-to-r from-yellow-400 via-amber-400 to-yellow-500 bg-clip-text text-transparent drop-shadow-2xl animate-pulse\",\n                                                        children: \"CHE NASCE DAL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block text-white drop-shadow-2xl\",\n                                                        children: \"MEDITERRANEO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl md:text-2xl text-gray-300 leading-relaxed max-w-2xl\",\n                                                children: [\n                                                    \"AfriCola Energy non \\xe8 una semplice bevanda: \\xe8 un'\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: \"esperienza di lusso\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \" che unisce la forza della Tunisia con l'eleganza italiana.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg text-gray-400 leading-relaxed max-w-xl\",\n                                                children: \"Ogni sorso ti trasporta nel cuore del Mediterraneo, dove tradizione millenaria e innovazione moderna si fondono in energia pura.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"group relative overflow-hidden bg-gradient-to-r from-yellow-400 to-amber-500 text-black px-12 py-5 text-lg font-black rounded-2xl hover:shadow-2xl hover:shadow-yellow-400/50 transition-all duration-500 transform hover:scale-105\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"relative z-10 flex items-center justify-center\",\n                                                        children: [\n                                                            \"PROVALA ORA\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-3 group-hover:translate-x-2 transition-transform text-xl\",\n                                                                children: \"⚡\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-amber-500 to-yellow-400 translate-x-full group-hover:translate-x-0 transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"group border-2 border-yellow-400 text-yellow-400 px-12 py-5 text-lg font-black rounded-2xl hover:bg-yellow-400 hover:text-black transition-all duration-300 backdrop-blur-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center justify-center\",\n                                                    children: [\n                                                        \"SCOPRI DI PI\\xd9\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-3 group-hover:rotate-45 transition-transform\",\n                                                            children: \"↗\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-96 h-96 border-2 border-yellow-400/40 rounded-full animate-spin-slow\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute w-80 h-80 border border-amber-400/30 rounded-full animate-spin-slow-reverse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute w-64 h-64 border border-yellow-300/50 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: canRef,\n                                            className: \"relative z-10 transform-gpu\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-yellow-400/30 via-amber-400/20 to-yellow-400/30 rounded-full blur-3xl scale-150 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"/africola-can.png\",\n                                                    alt: \"AfriCola Energy Can\",\n                                                    className: \"relative z-10 w-80 md:w-96 h-auto drop-shadow-2xl hover:scale-110 transition-all duration-700 transform-gpu\",\n                                                    style: {\n                                                        filter: \"drop-shadow(0 0 40px rgba(255, 215, 0, 0.6)) drop-shadow(0 0 80px rgba(255, 193, 7, 0.3))\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-10 -left-8 w-4 h-4 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full animate-bounce delay-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-20 -right-6 w-3 h-3 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full animate-bounce delay-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-32 right-16 w-2 h-2 bg-gradient-to-r from-yellow-300 to-amber-400 rounded-full animate-bounce delay-1000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-3 animate-bounce\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-sm font-medium tracking-wider\",\n                                    children: \"Scorri per scoprire\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-10 border-2 border-yellow-400/60 rounded-full flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-3 bg-gradient-to-b from-yellow-400 to-transparent rounded-full mt-2 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"prodotto\",\n                className: \"py-32 bg-gradient-to-b from-black to-gray-900 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(255,215,0,0.08)_0%,_transparent_70%)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-6 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-yellow-400 to-amber-500 bg-clip-text text-transparent text-sm font-black tracking-[0.3em] uppercase\",\n                                            children: \"⚡ FORMULA PREMIUM ⚡\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-5xl md:text-7xl font-black mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-white\",\n                                                children: \"LA NOSTRA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block bg-gradient-to-r from-yellow-400 to-amber-500 bg-clip-text text-transparent\",\n                                                children: \"FORMULA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                                        children: \"Ingredienti di lusso selezionati dal Mediterraneo per un'esperienza energetica senza compromessi\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20\",\n                                children: [\n                                    {\n                                        icon: \"⚡\",\n                                        title: \"CAFFEINA PREMIUM\",\n                                        desc: \"160mg di energia pura e naturale\",\n                                        gradient: \"from-yellow-400 to-amber-500\",\n                                        border: \"border-yellow-400/40\",\n                                        glow: \"hover:shadow-yellow-400/30\"\n                                    },\n                                    {\n                                        icon: \"\\uD83C\\uDF30\",\n                                        title: \"NOCE DI COLA\",\n                                        desc: \"Estratto autentico africano di lusso\",\n                                        gradient: \"from-amber-400 to-yellow-500\",\n                                        border: \"border-amber-400/40\",\n                                        glow: \"hover:shadow-amber-400/30\"\n                                    },\n                                    {\n                                        icon: \"\\uD83C\\uDFC6\",\n                                        title: \"QUALIT\\xc0 SUPREMA\",\n                                        desc: \"Standard italiani di eccellenza\",\n                                        gradient: \"from-yellow-300 to-amber-400\",\n                                        border: \"border-yellow-300/40\",\n                                        glow: \"hover:shadow-yellow-300/30\"\n                                    },\n                                    {\n                                        icon: \"✨\",\n                                        title: \"PUREZZA ASSOLUTA\",\n                                        desc: \"Zero compromessi, solo perfezione\",\n                                        gradient: \"from-amber-500 to-yellow-400\",\n                                        border: \"border-amber-500/40\",\n                                        glow: \"hover:shadow-amber-500/30\"\n                                    }\n                                ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `group relative bg-gray-900/50 backdrop-blur-sm p-8 rounded-3xl border ${feature.border} hover:border-opacity-80 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl ${feature.glow}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-10 rounded-3xl transition-all duration-500`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-5xl mb-6 group-hover:scale-125 transition-transform duration-500\",\n                                                        children: feature.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-black text-white mb-3 leading-tight\",\n                                                        children: feature.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm leading-relaxed\",\n                                                        children: feature.desc\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `w-12 h-1 bg-gradient-to-r ${feature.gradient} mx-auto mt-4 rounded-full group-hover:w-20 transition-all duration-500`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-gradient-to-r from-gray-900/60 via-black/40 to-gray-900/60 backdrop-blur-sm rounded-3xl p-12 border border-yellow-400/30 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-yellow-400/5 via-transparent to-amber-400/5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10 grid lg:grid-cols-2 gap-12 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-4xl md:text-5xl font-black mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-gradient-to-r from-yellow-400 to-amber-500 bg-clip-text text-transparent\",\n                                                                        children: \"ENERGIA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: \"DI LUSSO\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl text-gray-300 leading-relaxed mb-8\",\n                                                                children: \"Ogni lattina \\xe8 un capolavoro di ingegneria del gusto: l'essenza del Mediterraneo racchiusa in un'esperienza energetica senza precedenti.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-6\",\n                                                        children: [\n                                                            {\n                                                                label: \"Caffeina Premium\",\n                                                                value: \"160mg\"\n                                                            },\n                                                            {\n                                                                label: \"Calorie\",\n                                                                value: \"45\"\n                                                            },\n                                                            {\n                                                                label: \"Zuccheri\",\n                                                                value: \"0g\"\n                                                            },\n                                                            {\n                                                                label: \"Volume\",\n                                                                value: \"330ml\"\n                                                            }\n                                                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-4 bg-black/40 rounded-2xl border border-yellow-400/20 hover:border-yellow-400/40 transition-all duration-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-black bg-gradient-to-r from-yellow-400 to-amber-500 bg-clip-text text-transparent\",\n                                                                        children: stat.value\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 295,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: stat.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            \"Estratto naturale di noce di cola premium\",\n                                                            \"Formula energetica bilanciata per prestazioni ottimali\",\n                                                            \"Gusto intenso e rinfrescante di qualit\\xe0 superiore\",\n                                                            \"Zero zuccheri aggiunti, purezza assoluta garantita\"\n                                                        ].map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-300\",\n                                                                        children: benefit\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-r from-yellow-400/30 to-amber-400/30 rounded-full blur-3xl scale-150 animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"/africola-can.png\",\n                                                            alt: \"AfriCola Energy Premium\",\n                                                            className: \"relative z-10 w-72 h-auto drop-shadow-2xl hover:scale-110 transition-transform duration-700\",\n                                                            style: {\n                                                                filter: \"drop-shadow(0 0 30px rgba(255, 215, 0, 0.5))\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"energia\",\n                className: \"py-32 bg-gradient-to-b from-gray-900 to-black relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(255,193,7,0.08)_0%,_transparent_70%)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-6 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-5xl md:text-7xl font-black mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-white\",\n                                                children: \"UN PONTE TRA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block bg-gradient-to-r from-yellow-400 to-amber-500 bg-clip-text text-transparent\",\n                                                children: \"DUE CULTURE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed\",\n                                        children: \"Nel cuore del Mediterraneo, dove Africa ed Europa si incontrano, nasce l'energia pi\\xf9 pura e autentica\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-3 gap-12 items-center mb-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group text-center space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative mx-auto w-32 h-32 bg-gradient-to-br from-red-600 to-red-700 rounded-full flex items-center justify-center text-5xl group-hover:scale-110 transition-transform duration-500 border-4 border-yellow-400/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-br from-yellow-400/20 to-transparent rounded-full blur-xl group-hover:blur-2xl transition-all duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"relative z-10\",\n                                                        children: \"\\uD83C\\uDDF9\\uD83C\\uDDF3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-3xl font-black text-white mb-3\",\n                                                        children: \"TUNISIA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 leading-relaxed\",\n                                                        children: \"La forza dell'Africa, la vitalit\\xe0 del deserto, l'energia del sole mediterraneo che dona vita alla noce di cola pi\\xf9 pregiata.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group text-center space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative mx-auto w-40 h-40 bg-gradient-to-br from-yellow-400 via-amber-500 to-yellow-600 rounded-full flex items-center justify-center text-6xl group-hover:scale-110 transition-transform duration-500 border-4 border-white/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-br from-yellow-400/30 to-amber-400/30 rounded-full blur-xl group-hover:blur-2xl transition-all duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"relative z-10 text-black\",\n                                                        children: \"\\uD83C\\uDF0A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-4xl font-black bg-gradient-to-r from-yellow-400 to-amber-500 bg-clip-text text-transparent mb-4\",\n                                                        children: \"MEDITERRANEO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 leading-relaxed text-lg\",\n                                                        children: \"Il nostro mare dorato, ponte tra continenti, custode di tradizioni millenarie e innovazione senza tempo.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group text-center space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative mx-auto w-32 h-32 bg-gradient-to-br from-green-600 to-green-700 rounded-full flex items-center justify-center text-5xl group-hover:scale-110 transition-transform duration-500 border-4 border-yellow-400/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-br from-yellow-400/20 to-transparent rounded-full blur-xl group-hover:blur-2xl transition-all duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"relative z-10\",\n                                                        children: \"\\uD83C\\uDDEE\\uD83C\\uDDF9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-3xl font-black text-white mb-3\",\n                                                        children: \"ITALIA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 leading-relaxed\",\n                                                        children: \"L'arte del design, la passione per la perfezione, l'eleganza che trasforma ogni prodotto in un capolavoro di lusso.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-3 gap-8\",\n                                children: [\n                                    {\n                                        title: \"AUTENTICIT\\xc0\",\n                                        desc: \"Ingredienti naturali selezionati dalle migliori fonti mediterranee di qualit\\xe0 suprema\",\n                                        icon: \"\\uD83C\\uDF3F\",\n                                        gradient: \"from-yellow-400 to-amber-500\"\n                                    },\n                                    {\n                                        title: \"TRADIZIONE\",\n                                        desc: \"Ricette che onorano la storia millenaria e la cultura del nostro territorio dorato\",\n                                        icon: \"\\uD83C\\uDFDB️\",\n                                        gradient: \"from-amber-400 to-yellow-500\"\n                                    },\n                                    {\n                                        title: \"ECCELLENZA\",\n                                        desc: \"Tecnologie all'avanguardia per preservare la purezza e l'intensit\\xe0 degli ingredienti\",\n                                        icon: \"⚡\",\n                                        gradient: \"from-yellow-300 to-amber-400\"\n                                    }\n                                ].map((value, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group text-center p-8 bg-gray-900/40 backdrop-blur-sm rounded-3xl border border-yellow-400/20 hover:border-yellow-400/50 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-yellow-400/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-5xl mb-6 group-hover:scale-125 transition-transform duration-500\",\n                                                children: value.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: `text-2xl font-black mb-4 bg-gradient-to-r ${value.gradient} bg-clip-text text-transparent`,\n                                                children: value.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 leading-relaxed\",\n                                                children: value.desc\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"acquista\",\n                className: \"py-32 bg-gradient-to-b from-black to-gray-900 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(255,215,0,0.08)_0%,_transparent_70%)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-6 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-5xl md:text-7xl font-black mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-white\",\n                                                children: \"SCEGLI LA TUA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block bg-gradient-to-r from-yellow-400 to-amber-500 bg-clip-text text-transparent\",\n                                                children: \"ESPERIENZA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                        children: \"Energia mediterranea di lusso disponibile in confezioni premium per ogni momento della tua giornata\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-3 gap-8 mb-16\",\n                                children: [\n                                    {\n                                        name: \"ASSAGGIO\",\n                                        quantity: \"1 LATTINA\",\n                                        price: \"2.50\",\n                                        desc: \"Perfetta per scoprire l'eccellenza\",\n                                        features: [\n                                            \"Prima esperienza premium\",\n                                            \"Gusto autentico di lusso\",\n                                            \"Energia immediata e pura\"\n                                        ],\n                                        popular: false,\n                                        gradient: \"from-gray-800/60 to-gray-900/60\",\n                                        border: \"border-gray-600/50\",\n                                        textColor: \"text-white\"\n                                    },\n                                    {\n                                        name: \"ENERGIA\",\n                                        quantity: \"6 LATTINE\",\n                                        price: \"14.00\",\n                                        desc: \"Ideale per la settimana di successo\",\n                                        features: [\n                                            \"Risparmio garantito\",\n                                            \"Energia costante premium\",\n                                            \"Confezione elegante\"\n                                        ],\n                                        popular: true,\n                                        gradient: \"from-yellow-400/20 to-amber-500/20\",\n                                        border: \"border-yellow-400\",\n                                        textColor: \"text-yellow-400\"\n                                    },\n                                    {\n                                        name: \"POTENZA\",\n                                        quantity: \"12 LATTINE\",\n                                        price: \"26.00\",\n                                        desc: \"Per i veri intenditori di lusso\",\n                                        features: [\n                                            \"Massimo risparmio\",\n                                            \"Scorta mensile premium\",\n                                            \"Qualit\\xe0 suprema garantita\"\n                                        ],\n                                        popular: false,\n                                        gradient: \"from-amber-400/20 to-yellow-400/20\",\n                                        border: \"border-amber-400/60\",\n                                        textColor: \"text-amber-400\"\n                                    }\n                                ].map((pack, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `group relative bg-gradient-to-br ${pack.gradient} backdrop-blur-sm p-8 rounded-3xl border ${pack.border} transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl ${pack.popular ? \"scale-105 shadow-xl shadow-yellow-400/30 border-2\" : \"hover:shadow-yellow-400/20\"}`,\n                                        children: [\n                                            pack.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-6 left-1/2 transform -translate-x-1/2 z-20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-yellow-400 to-amber-500 text-black px-6 py-3 rounded-full text-sm font-black tracking-wider animate-pulse\",\n                                                    children: \"⚡ PI\\xd9 SCELTO ⚡\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `text-sm font-black ${pack.textColor} tracking-wider mb-2`,\n                                                                children: pack.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-black text-white mb-2\",\n                                                                children: pack.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 511,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400\",\n                                                                children: pack.desc\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-5xl font-black text-white\",\n                                                                children: [\n                                                                    pack.price,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xl text-yellow-400 ml-2\",\n                                                                        children: \"TND\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-2\",\n                                                                children: pack.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        className: \"flex items-center justify-center text-sm text-gray-300\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"w-1.5 h-1.5 bg-yellow-400 rounded-full mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 524,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            feature\n                                                                        ]\n                                                                    }, idx, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 523,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"w-full bg-gradient-to-r from-yellow-400 to-amber-500 text-black py-4 rounded-2xl font-black text-lg hover:shadow-2xl hover:shadow-yellow-400/50 transition-all duration-500 transform hover:scale-105\",\n                                                        children: \"ORDINA ORA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center space-x-4 bg-gradient-to-r from-yellow-400/20 to-amber-400/20 backdrop-blur-sm border border-yellow-400/40 px-8 py-4 rounded-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83D\\uDE9A\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-yellow-400 font-black text-lg\",\n                                                children: \"Spedizione gratuita in tutta la Tunisia\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"group bg-transparent border-2 border-yellow-400 text-yellow-400 px-10 py-4 rounded-2xl font-black text-lg hover:bg-yellow-400 hover:text-black transition-all duration-500 backdrop-blur-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                \"\\uD83D\\uDCCD TROVA IL PUNTO VENDITA PI\\xd9 VICINO\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-3 group-hover:translate-x-2 transition-transform\",\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 444,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"contatti\",\n                className: \"py-32 bg-gradient-to-b from-gray-900 to-black relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-5xl md:text-7xl font-black mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block bg-gradient-to-r from-yellow-400 to-amber-500 bg-clip-text text-transparent\",\n                                            children: \"UNISCITI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-white\",\n                                            children: \"ALLA FAMIGLIA\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed\",\n                                    children: \"Diventa parte dell'\\xe9lite energetica mediterranea. Contattaci per partnership esclusive e collaborazioni di lusso.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                            lineNumber: 558,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-2 gap-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        {\n                                            icon: \"\\uD83D\\uDCE7\",\n                                            title: \"EMAIL\",\n                                            info: \"<EMAIL>\",\n                                            gradient: \"from-yellow-400 to-amber-500\",\n                                            bg: \"from-yellow-400/20 to-amber-400/20\"\n                                        },\n                                        {\n                                            icon: \"\\uD83D\\uDCF1\",\n                                            title: \"TELEFONO\",\n                                            info: \"+216 XX XXX XXX\",\n                                            gradient: \"from-amber-400 to-yellow-500\",\n                                            bg: \"from-amber-400/20 to-yellow-400/20\"\n                                        },\n                                        {\n                                            icon: \"\\uD83D\\uDCCD\",\n                                            title: \"SEDE\",\n                                            info: \"Tunis, Tunisia\",\n                                            gradient: \"from-yellow-300 to-amber-400\",\n                                            bg: \"from-yellow-300/20 to-amber-400/20\"\n                                        }\n                                    ].map((contact, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `group flex items-center space-x-6 p-6 bg-gradient-to-r ${contact.bg} backdrop-blur-sm rounded-3xl border border-yellow-400/30 hover:border-yellow-400/60 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-yellow-400/20`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-16 h-16 bg-gradient-to-r ${contact.gradient} rounded-full flex items-center justify-center text-2xl text-black group-hover:scale-110 transition-transform duration-500`,\n                                                    children: contact.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: `text-xl font-black bg-gradient-to-r ${contact.gradient} bg-clip-text text-transparent`,\n                                                            children: contact.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 text-lg\",\n                                                            children: contact.info\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-gray-900/60 to-black/60 backdrop-blur-sm p-10 rounded-3xl border border-yellow-400/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-3xl font-black bg-gradient-to-r from-yellow-400 to-amber-500 bg-clip-text text-transparent mb-8 text-center\",\n                                            children: \"DIVENTA PARTNER\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Nome\",\n                                                            className: \"bg-black/40 border border-yellow-400/30 rounded-2xl px-6 py-4 text-white placeholder-gray-400 focus:border-yellow-400 focus:outline-none transition-all duration-300 backdrop-blur-sm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Cognome\",\n                                                            className: \"bg-black/40 border border-yellow-400/30 rounded-2xl px-6 py-4 text-white placeholder-gray-400 focus:border-yellow-400 focus:outline-none transition-all duration-300 backdrop-blur-sm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                            lineNumber: 628,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    placeholder: \"Email\",\n                                                    className: \"w-full bg-black/40 border border-yellow-400/30 rounded-2xl px-6 py-4 text-white placeholder-gray-400 focus:border-yellow-400 focus:outline-none transition-all duration-300 backdrop-blur-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Nome Attivit\\xe0\",\n                                                    className: \"w-full bg-black/40 border border-yellow-400/30 rounded-2xl px-6 py-4 text-white placeholder-gray-400 focus:border-yellow-400 focus:outline-none transition-all duration-300 backdrop-blur-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 639,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    placeholder: \"Raccontaci della tua attivit\\xe0 e dei tuoi obiettivi di partnership...\",\n                                                    rows: 4,\n                                                    className: \"w-full bg-black/40 border border-yellow-400/30 rounded-2xl px-6 py-4 text-white placeholder-gray-400 focus:border-yellow-400 focus:outline-none resize-none transition-all duration-300 backdrop-blur-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    className: \"w-full bg-gradient-to-r from-yellow-400 to-amber-500 text-black py-4 rounded-2xl font-black text-lg hover:shadow-2xl hover:shadow-yellow-400/50 transition-all duration-500 transform hover:scale-105\",\n                                                    children: \"INVIA RICHIESTA ⚡\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                    lineNumber: 557,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 556,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gradient-to-t from-black to-gray-900 border-t border-yellow-400/30 py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-black bg-gradient-to-r from-yellow-400 via-amber-400 to-yellow-500 bg-clip-text text-transparent\",\n                                        children: \"AFRICOLA ENERGY\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-400\",\n                                        children: \"L'energia di lusso che nasce dal Mediterraneo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center space-x-8 text-gray-500 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\xa9 2024 AfriCola Energy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-400\",\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Made in Tunisia \\uD83C\\uDDF9\\uD83C\\uDDF3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-400\",\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Designed with Italian Passion \\uD83C\\uDDEE\\uD83C\\uDDF9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 672,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 681,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full animate-pulse delay-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-gradient-to-r from-yellow-300 to-amber-400 rounded-full animate-pulse delay-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                                lineNumber: 680,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                        lineNumber: 664,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                    lineNumber: 663,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n                lineNumber: 662,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\page.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6ca894994e4a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZnJpY29sYS1lbmVyZ3ktd2Vic2l0ZS8uL2FwcC9nbG9iYWxzLmNzcz9kYTZhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNmNhODk0OTk0ZTRhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"AfriCola Energy - Two Shores. One Energy.\",\n    description: \"AfriCola Energy bridges Tunisian heritage with Italian design. Experience the Mediterranean energy revolution with three legendary flavors: Kola Nut, Classic, and Light.\",\n    keywords: \"AfriCola, Energy Drink, Tunisia, Italia, Mediterranean, Kola Nut, Energy, Cultural Fusion, Premium Beverages\",\n    authors: [\n        {\n            name: \"AfriCola International\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    openGraph: {\n        title: \"AfriCola Energy - Two Shores. One Energy.\",\n        description: \"Mediterranean energy revolution bridging Tunisia and Italy\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"AfriCola Energy - Mediterranean Energy Revolution\",\n        description: \"Three legendary flavors. One cultural bridge. \\uD83C\\uDDF9\\uD83C\\uDDF3⚡\\uD83C\\uDDEE\\uD83C\\uDDF9\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\app\\\\layout.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFc0I7QUFFZixNQUFNQSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUNFO0lBQ0ZDLFVBQ0U7SUFDRkMsU0FBUztRQUFDO1lBQUVDLE1BQU07UUFBeUI7S0FBRTtJQUM3Q0MsVUFBVTtJQUNWQyxXQUFXO1FBQ1ROLE9BQU87UUFDUEMsYUFBYTtRQUNiTSxNQUFNO1FBQ05DLFFBQVE7SUFDVjtJQUNBQyxTQUFTO1FBQ1BDLE1BQU07UUFDTlYsT0FBTztRQUNQQyxhQUFhO0lBQ2Y7QUFDRixFQUFDO0FBRWMsU0FBU1UsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7OzBCQUNULDhEQUFDQzs7a0NBQ0MsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFhQyxNQUFLOzs7Ozs7a0NBQzVCLDhEQUFDRjt3QkFBS0MsS0FBSTt3QkFBYUMsTUFBSzt3QkFBNEJDLGFBQVk7Ozs7Ozs7Ozs7OzswQkFFdEUsOERBQUNDO2dCQUFLQyxXQUFVOzBCQUFlVDs7Ozs7Ozs7Ozs7O0FBR3JDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWZyaWNvbGEtZW5lcmd5LXdlYnNpdGUvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIFJlYWN0IGZyb20gXCJyZWFjdFwiXHJcbmltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiXHJcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIlxyXG5cclxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcclxuICB0aXRsZTogXCJBZnJpQ29sYSBFbmVyZ3kgLSBUd28gU2hvcmVzLiBPbmUgRW5lcmd5LlwiLFxyXG4gIGRlc2NyaXB0aW9uOlxyXG4gICAgXCJBZnJpQ29sYSBFbmVyZ3kgYnJpZGdlcyBUdW5pc2lhbiBoZXJpdGFnZSB3aXRoIEl0YWxpYW4gZGVzaWduLiBFeHBlcmllbmNlIHRoZSBNZWRpdGVycmFuZWFuIGVuZXJneSByZXZvbHV0aW9uIHdpdGggdGhyZWUgbGVnZW5kYXJ5IGZsYXZvcnM6IEtvbGEgTnV0LCBDbGFzc2ljLCBhbmQgTGlnaHQuXCIsXHJcbiAga2V5d29yZHM6XHJcbiAgICBcIkFmcmlDb2xhLCBFbmVyZ3kgRHJpbmssIFR1bmlzaWEsIEl0YWxpYSwgTWVkaXRlcnJhbmVhbiwgS29sYSBOdXQsIEVuZXJneSwgQ3VsdHVyYWwgRnVzaW9uLCBQcmVtaXVtIEJldmVyYWdlc1wiLFxyXG4gIGF1dGhvcnM6IFt7IG5hbWU6IFwiQWZyaUNvbGEgSW50ZXJuYXRpb25hbFwiIH1dLFxyXG4gIHZpZXdwb3J0OiBcIndpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xXCIsXHJcbiAgb3BlbkdyYXBoOiB7XHJcbiAgICB0aXRsZTogXCJBZnJpQ29sYSBFbmVyZ3kgLSBUd28gU2hvcmVzLiBPbmUgRW5lcmd5LlwiLFxyXG4gICAgZGVzY3JpcHRpb246IFwiTWVkaXRlcnJhbmVhbiBlbmVyZ3kgcmV2b2x1dGlvbiBicmlkZ2luZyBUdW5pc2lhIGFuZCBJdGFseVwiLFxyXG4gICAgdHlwZTogXCJ3ZWJzaXRlXCIsXHJcbiAgICBsb2NhbGU6IFwiZW5fVVNcIixcclxuICB9LFxyXG4gIHR3aXR0ZXI6IHtcclxuICAgIGNhcmQ6IFwic3VtbWFyeV9sYXJnZV9pbWFnZVwiLFxyXG4gICAgdGl0bGU6IFwiQWZyaUNvbGEgRW5lcmd5IC0gTWVkaXRlcnJhbmVhbiBFbmVyZ3kgUmV2b2x1dGlvblwiLFxyXG4gICAgZGVzY3JpcHRpb246IFwiVGhyZWUgbGVnZW5kYXJ5IGZsYXZvcnMuIE9uZSBjdWx0dXJhbCBicmlkZ2UuIPCfh7nwn4ez4pqh8J+HrvCfh7lcIixcclxuICB9LFxyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufToge1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cclxuICAgICAgPGhlYWQ+XHJcbiAgICAgICAgPGxpbmsgcmVsPVwicHJlY29ubmVjdFwiIGhyZWY9XCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tXCIgLz5cclxuICAgICAgICA8bGluayByZWw9XCJwcmVjb25uZWN0XCIgaHJlZj1cImh0dHBzOi8vZm9udHMuZ3N0YXRpYy5jb21cIiBjcm9zc09yaWdpbj1cImFub255bW91c1wiIC8+XHJcbiAgICAgIDwvaGVhZD5cclxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiYW50aWFsaWFzZWRcIj57Y2hpbGRyZW59PC9ib2R5PlxyXG4gICAgPC9odG1sPlxyXG4gIClcclxufVxyXG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwia2V5d29yZHMiLCJhdXRob3JzIiwibmFtZSIsInZpZXdwb3J0Iiwib3BlbkdyYXBoIiwidHlwZSIsImxvY2FsZSIsInR3aXR0ZXIiLCJjYXJkIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJoZWFkIiwibGluayIsInJlbCIsImhyZWYiLCJjcm9zc09yaWdpbiIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\test\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMEGA-PC%5CDesktop%5Ctest&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();